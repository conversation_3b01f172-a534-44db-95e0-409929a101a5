const { sequelize, connectDb, models } = require("../config/db");
const seedDatabase = require("../utils/seed");

const runSeed = async () => {
  try {
    // Get command line arguments
    const args = process.argv.slice(2);
    const options = {
      seed: true,
      force: args.includes('--force'),
      alter: !args.includes('--force') && args.includes('--alter'),
      ignoreDuplicates: !args.includes('--no-ignore-duplicates')
    };

    // Connect to the database with options
    await connectDb(options);
    process.exit(0);
  } catch (error) {
    console.error("Error seeding database:", error);
    process.exit(1);
  }
};

runSeed();
