const express = require("express");
const router = express.Router();
const {
  createPaymentOrder,
  verifyPayment,
  handleWebhook,
  getPaymentStatus,
  getUserPayments,
  getClubEarnings,
  createBulkPaymentOrder,
  verifyBulkPayment,
  handleBulkWebhook,
  getClubPayments,
  getPendingBulkRegistrations,
  getPaymentReceipt,
} = require("../controllers/paymentController");
const verifyJwt = require("../middlewares/verifyJwt");
const refundRouter = require("./refund");
router.use("/refund", refundRouter);
// Payment initiation route (requires authentication)
router.post("/create-order", verifyJwt, createPaymentOrder);
router.post("/create-bulk-order", verifyJwt, createBulkPaymentOrder);

// Payment callback routes (no authentication required as they are called by Razorpay)
router.post("/verify-payment", verifyJwt, verifyPayment);
router.post("/verify-bulk-payment", verifyJwt, verifyBulkPayment);
router.post(
  "/webhook",
  express.raw({ type: "application/json" }),
  handleWebhook
);
router.post(
  "/bulk-webhook",
  express.raw({ type: "application/json" }),
  handleBulkWebhook
);

// Payment status routes (requires authentication)
router.get("/status/:paymentId", verifyJwt, getPaymentStatus);
router.get("/user", verifyJwt, getUserPayments);
router.get("/club/tournament", verifyJwt, getClubEarnings);
router.get("/club", verifyJwt, getClubPayments);
router.get(
  "/club/pending-registrations",
  verifyJwt,
  getPendingBulkRegistrations
);
router.get("/receipt", verifyJwt, getPaymentReceipt);

module.exports = router;
