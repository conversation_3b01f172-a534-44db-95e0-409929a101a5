const express = require("express");
const router = express.Router({ mergeParams: true });
const {
  getAllArbiter,
  getSingleArbiter,
  getArbiterProfile,
  createArbiterProfile,
  getArbiterTournaments,
  editArbiterProfile,
  getArbiterTournamentInvite,
  getArbiterForTournament,
  updateTournamentInvite,
  tournamentPairingNotificationsSms,
  reportGenerate,
  removeProfileImage,
} = require("../controllers/arbiterController");
const verifyJwt = require("../middlewares/verifyJwt");
const { uploadFactory, handleUploadError } = require("../utils/s3");
const profileRouter = express.Router();

router.use("/profile", profileRouter);
router.get("/", getAllArbiter);
router.get("/report", reportGenerate);

router.get("/single/:id", getSingleArbiter);
router.post(
  "/pairing-notifications",
  verifyJwt,
  tournamentPairingNotificationsSms
);
// profile router
profileRouter.get("/", verifyJwt, getArbiterProfile);
profileRouter.post(
  "/",
  verifyJwt,
  uploadFactory.arbiter.profileImage(),
  handleUploadError,
  createArbiterProfile
);
profileRouter.put(
  "/",
  verifyJwt,
  uploadFactory.arbiter.profileImage(),
  handleUploadError,
  editArbiterProfile
);
profileRouter.get("/tournaments", verifyJwt, getArbiterTournaments);
profileRouter.get("/arbiter", getArbiterForTournament);
profileRouter.get("/tournament-invite", verifyJwt, getArbiterTournamentInvite);
profileRouter.post("/tournament-invite", verifyJwt, updateTournamentInvite);
profileRouter.delete("/remove-profile-image", verifyJwt, removeProfileImage);

module.exports = router;
