const express = require("express");
const router = express.Router();
const {
  initiateRefund,
  getRefundStatus,
  handleRefundWebhook,
} = require("../controllers/refundController");
const verifyJwt = require("../middlewares/verifyJwt");

// Initiate a refund
router.post("/initiate", verifyJwt, initiateRefund);

// Get refund status
router.get("/status/:refundId", verifyJwt, getRefundStatus) ;

// Handle refund webhook
router.post(
  "/webhook",
  express.raw({ type: "application/json" }),
  handleRefundWebhook
);

module.exports = router;
