const express = require("express");
const router = express.Router({ mergeParams: true });
const { getSinglePlayer, 
    getClubDetailById, 
    getSingleArbiter, 
    getPayment, 
    getAllTournament, 
    } = require("../../controllers/admin/DetailsController");

router.get('/single/:id',getSinglePlayer)
router.get('/single/club/:id',getClubDetailById)
router.get('/single/arbiter/:id',getSingleArbiter)
router.get('/payment',getPayment)
router.get('/payment/tournament',getAllTournament)


module.exports = router;