const express = require("express");
const router = express.Router();
const profileRouter = express.Router();
const {
  TournamentRegPlayers,
  PublicPlayerReport,
  PaymentReport,
  clubPlayers,
} = require("../controllers/reportGen");
const verifyJwt = require("../middlewares/verifyJwt");
const { uploadFactory, handleUploadError } = require("../utils/s3");

router.use(verifyJwt);
router.get("/players-tournament", TournamentRegPlayers);
router.get("/public-players", PublicPlayerReport);
router.get("/payment", PaymentReport);
router.get("/club-players/:id", clubPlayers);

module.exports = router;
