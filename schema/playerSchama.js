const z = require("zod");
const playerDetailSchema = z.object({
  playerTitle: z.string().max(50).optional().default("Untitled"),
  profileUrl: z.string().url().optional(),
  dob: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  gender: z.enum(["male", "female", "other"]),
  parentGuardianName: z.string().max(100).optional(),
  phoneChanged: z.coerce.boolean().optional().default(false),
  emergencyContact: z.string().max(15).optional(),
  alternateContact: z.string().max(15).optional(),
  fideRating: z.string().optional().default("Unrated"),
  fideId: z.string().max(20).optional(),

  aicfId: z.string().max(20).optional(),
  stateId: z.string().max(20).optional(),
  districtId: z.string().max(20).optional(),
  association: z.string().max(100).optional(),
  club: z.string().max(100).optional(),
  other_club: z.boolean().optional(),
  country: z.string().max(50).default("India"),
  countryCode: z.string().max(4).default("IN"),
  state: z.string().max(50).default("Tamilnadu"),
  district: z.string().max(50).default("Chennai"),
  city: z.string().max(50).default("Chennai"),
  pincode: z.string().max(10),
  address: z.string().optional(),
  termsAndConditions: z.coerce.boolean(),
  clubs: z.preprocess(
    (val) => (typeof val === 'string' ? JSON.parse(val) : val),
    z.object({
      clubName: z.string().max(100).optional(),
      clubId: z.string().max(100).optional(), 
      id: z.string().max(100).optional(),
    }).optional()
  ),
  other_clubs: z.enum(["true", "false"]).default("false"),
});
const updatePlayerSchema = z.object({
  playerTitle: z.string().max(50).optional().default("Untitled"),
  profileUrl: z.string().url().optional(),
  dob: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  gender: z.enum(["male", "female", "other"]),
  parentGuardianName: z.string().max(100).optional(),
  phoneChanged: z.coerce.boolean().optional().default(false),
  emergencyContact: z.string().max(15).optional(),
  alternateContact: z.string().max(15).optional(),
  fideRating: z.string().optional().default("Unrated"),
  fideId: z.string().max(20).optional(),
  name: z.coerce.string().min(3).max(50),
  phoneNumber: z
    .string()
    .regex(/^(91)?\d{10}$/, {
      message: "Phone number must start with 91 followed by 10 digits",
    })
    .max(12),
  aicfId: z.string().max(20).optional(),
  stateId: z.string().max(20).optional(),
  districtId: z.string().max(20).optional(),
  association: z.string().max(100).optional(),
  club: z.string().max(100).optional(),
  other_club: z.boolean().optional(),
  country: z.string().max(50).default("India"),
  countryCode: z.string().max(4).default("IN"),
  state: z.string().max(50).default("Tamilnadu"),
  district: z.string().max(50).default("Chennai"),
  city: z.string().max(50).default("Chennai"),
  pincode: z.string().max(10),
  address: z.string().optional(),
  termsAndConditions: z.coerce.boolean(),
  clubs: z.preprocess(
    (val) => (typeof val === 'string' ? JSON.parse(val) : val),
    z.object({
      clubName: z.string().max(100).optional(),
      clubId: z.string().max(100).optional(), 
      id: z.string().max(100).optional(),
    }).optional()
  ),
  other_clubs: z.enum(["true", "false"]).default("false"),
});

const getAllPlayerSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  city: z.string().optional(),
  playerId: z.string().optional(),
  playerName: z.string().optional(),
  country: z.string().optional(),
  state: z.string().optional(),
  district: z.string().optional(),
  email: z.string().optional(),
  mobile: z.string().optional(),
});

const getAllArbiterSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  city: z.string().optional(),
  arbiterId: z.string().optional(),
  arbiterName: z.string().optional(),
  country: z.string().optional(),
  state: z.string().optional(),
  district: z.string().optional(),
});

module.exports = {
  playerDetailSchema,
  getAllPlayerSchema,
  getAllArbiterSchema,
  updatePlayerSchema,
};
