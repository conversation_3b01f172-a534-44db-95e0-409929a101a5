const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class BulkRegistration extends Model {
    static associate(models) {
      BulkRegistration.belongsTo(models.Tournament, {
        foreignKey: "tournamentId",
        as: "tournament",
      });

      BulkRegistration.belongsTo(models.ClubDetail, {
        foreignKey: "registeredBy",
        as: "club",
      });
      BulkRegistration.hasOne(models.Payment, {
        foreignKey: "bulkRegistrationId",
        as: "payment",
      });
    }
  }

  BulkRegistration.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      bulkRegistrationId: {
        type: DataTypes.STRING,
        unique: true,
        field: "bulk_registration_id",
      },
      tournamentId: {
        type: DataTypes.UUID,
        allowNull: false,
        field: "tournament_id",
        references: {
          model: "tournament",
          key: "id",
        },
      },
      registeredBy: {
        type: DataTypes.UUID,
        allowNull: false,
        field: "registered_by",
      },

      registrationStatus: {
        type: DataTypes.ENUM("pending", "registered", "rejected"),
        allowNull: false,
        defaultValue: "pending",
        field: "registration_status",
      },
      playerList: {
        type: DataTypes.JSONB,
        allowNull: false,
        field: "player_list", // List of player objects
      },
      totalAmount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        field: "total_amount",
      },
      playersCount:{
        type: DataTypes.INTEGER,
        allowNull: true,
        field: "players_count",
      },
      remarks: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "BulkRegistration",
      tableName: "bulk_registrations",
      timestamps: true,
    }
  );
  BulkRegistration.beforeCreate(async (registration, options) => {
    const max = await BulkRegistration.count();
    const nextNumber = max + 1;
    registration.bulkRegistrationId = `br-${nextNumber}`;
  });

  return BulkRegistration;
};
