const { Model, DataTypes } = require("sequelize");
module.exports = (sequelize) => {
  class PlayerDetail extends Model {
    static associate(models) {
      PlayerDetail.belongsTo(models.User, {
        foreignKey: "userId",
        onDelete: "CASCADE",
      });
    PlayerDetail.belongsTo(models.ClubDetail, {
    foreignKey: "clubId",
    as: "clubs", 
  });
    }
  }

  PlayerDetail.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      playerTitle: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: "Untitled",
        field: "player_title",
      },
      profileUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        validate: {
          isUrl: true,
        },
        field: "profile_url",
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        field: "user_id",
      },
      dob: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      gender: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      parentGuardianName: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "",
        field: "parent_guardian_name",
      },
      emergencyContact: {
        type: DataTypes.STRING(15),
        allowNull: true,
        defaultValue: "",
        field: "emergency_contact",
      },
      alternateContact: {
        type: DataTypes.STRING(15),
        allowNull: true,
        defaultValue: "",
        field: "alternate_contact",
      },
      fideRating: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: "Unrated",
        field: "fide_rating",
      },
      fideId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "",
        field: "fide_id",
      },
      aicfId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "",
        field: "aicf_id",
      },
      stateId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "",
        field: "state_id",
      },
      districtId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        defaultValue: "",
        field: "district_id",
      },
      association: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      club: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: "",
      },
      clubId: {
        type: DataTypes.UUID,
        allowNull: true,

        references: {
          model: "club_details",
          key: "id",
        },
        field: "club_id",
      },
      country: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: "India",
      },
      state: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: "Tamilnadu",
      },
      district: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: "Chennai",
      },
      city: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: "Chennai",
      },
      pincode: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      address: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      myFiles: {
        type: DataTypes.JSONB,
        allowNull: true,
        defaultValue: [],
        field: "my_files",
      },
      termsAndConditions: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        field: "terms_and_conditions",
      },
      friendIds: {
        type: DataTypes.ARRAY(DataTypes.UUID),
        allowNull: false,
        defaultValue: [],
        field: "friend_ids",
      },
    },
    {
      sequelize,
      modelName: "PlayerDetail",
      tableName: "players",
      timestamps: true,
    }
  );

  return PlayerDetail;
};
