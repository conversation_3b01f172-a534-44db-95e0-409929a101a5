const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Payment extends Model {
    static associate(models) {
      Payment.belongsTo(models.Tournament, {
        foreignKey: "tournamentId",
        as: "tournament",
      });
      Payment.belongsTo(models.Registration, {
        foreignKey: "registrationId",
        as: "registration",
      });
      Payment.belongsTo(models.BulkRegistration, {
        foreignKey: "bulkRegistrationId",
        as: "bulkRegistration",
      });
      Payment.hasMany(models.Registration, { 
        foreignKey: 'payment_id', 
        as: 'registrations' 
      });
      Payment.belongsTo(models.User, { 
        foreignKey: "userId", 
        as: "user" 
      });
      // Payment.hasMany(models.Refund, { 
      //   foreignKey: 'paymentId', 
      //   as: 'refunds' 
      // });
    }
  }

  Payment.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        field: 'user_id',
      },
      tournamentId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "tournament",
          key: "id",
        },
        field: 'tournament_id',
      },
      registrationId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "registrations",
          key: "id",
        },
        field: 'registration_id',
      },
      bulkRegistrationId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "bulk_registrations",
          key: "id",
        },
        field: 'bulk_registration_id',
      },
      // Razorpay specific fields
      razorpayOrderId: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: true,
        field: 'razorpay_order_id',
      },
      razorpayPaymentId: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: true,
        field: 'razorpay_payment_id',
      },
      razorpaySignature: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'razorpay_signature',
      },
      paymentDate: {
        type: DataTypes.DATE, // Changed from DATEONLY to include time
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'payment_date',
      },
      paymentStatus: {
        type: DataTypes.ENUM("created", "pending", "authorized", "captured", "failed", "refunded"),
        allowNull: false,
        defaultValue: "created",
        field: 'payment_status',
      },
      paymentTransactionId: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'payment_transaction_id',
      },
      paymentAmount: {
        type: DataTypes.INTEGER, // Razorpay uses paise (smallest currency unit)
        allowNull: false,
        field: 'payment_amount',
      },
      paymentCurrency: {
        type: DataTypes.STRING(3),
        allowNull: false,
        defaultValue: 'INR',
        field: 'payment_currency',
      },
      paymentMethod: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'payment_method',
      },
      paymentReference: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'payment_reference',
      },
      paymentType: {
        type: DataTypes.ENUM("player", "club"),
        allowNull: false,
        field: 'payment_type',
      },
      paymentRemarks: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: 'payment_remarks',
      },
      // Store complete Razorpay webhook/response data
      razorpayResponse: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: 'razorpay_response',
      },
      // Additional Razorpay fields
      razorpayOrderStatus: {
        type: DataTypes.ENUM("created", "attempted", "paid"),
        allowNull: true,
        field: 'razorpay_order_status',
      },
      razorpayAttempts: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
        field: 'razorpay_attempts',
      },

    },
    {
      sequelize,
      modelName: "Payment",
      tableName: "payments",
      indexes: [
        {
          fields: ['razorpay_order_id']
        },
        {
          fields: ['razorpay_payment_id']
        },
        {
          fields: ['payment_status']
        },
        {
          fields: ['user_id', 'tournament_id']
        }
      ]
    }
  );

  return Payment;
};