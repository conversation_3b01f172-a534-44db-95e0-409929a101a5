const { Model, DataTypes } = require("sequelize");
module.exports = (sequelize) => {
  class Certificate extends Model {
    static associate(models) {
      Certificate.belongsTo(models.Tournament, {
        foreignKey: "tournamentId",
        as: "tournament",
      });
      Certificate.belongsTo(models.User, {
        foreignKey: "userId",
        as: "user",
      });
    }
  }
  Certificate.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      tournamentId: {
        type: DataTypes.UUID,
        allowNull: false,
        field: "tournament_id",
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: true,
        field: "user_id",
      },
      certificateId: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "certificate_id",
      },
      playerName: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "player_name",
      },
      totalPoints: {
        type: DataTypes.FLOAT,
        allowNull: false,
        field: "total_points",
      },
      rank: {
        type: DataTypes.INTEGER,
        allowNull: false,
        field: "rank",
      },
      ageCategory: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "age_category",
      },
      genderCategory: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "gender_category",
      },
      roundId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        field: "round_id",
      },
      organization: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "organization",
      },
      poweredBy: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "powered_by",
      },
      sponsorName: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "sponsor_name",
      },
      subtitle: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "subtitle",
      },
      tournamentDate: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "tournament_date",
      },
      venue: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "venue",
      },
      templateId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
        field: "template_id",
      },
      tournamentTitle: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "tournament_title",
      },
      metadata: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "Certificate",
      tableName: "certificates",
      timestamps: true,
    }
  );

  return Certificate;
};
