const { Model, DataTypes, Op } = require("sequelize");

module.exports = (sequelize) => {
  class Notifications extends Model {
    static associate(models) {
      // Association with recipient user
      Notifications.belongsTo(models.User, {
        foreignKey: "userId",
        as: "recipient",
      });

      // Optional: Association with creator user
      Notifications.belongsTo(models.User, {
        foreignKey: "creatorId",
        as: "creator",
      });
    }

    /**
     * Mark notification as sent
     */
    async markAsSent() {
      this.status = "delivered";
      this.sentAt = new Date();
      this.deliveryAttempts += 1;
      return this.save();
    }

    /**
     * Mark notification as failed
     * @param {string} errorMessage - Error message to store
     */
    async markAsFailed(errorMessage) {
      this.deliveryAttempts += 1;

      // If max attempts reached, mark as permanently failed
      if (this.deliveryAttempts >= this.maxAttempts) {
        this.status = "failed";
        this.failureReason = errorMessage.substring(0, 255);
      }

      this.lastAttemptAt = new Date();

      // Parse metadata safely
      let currentMetadata;
      try {
        currentMetadata =
          typeof this.metadata === "string"
            ? JSON.parse(this.metadata || "{}")
            : this.metadata || {};
      } catch (e) {
        currentMetadata = {};
      }

      // Get attempt history or initialize empty array
      const attemptHistory = currentMetadata.attemptHistory || [];

      this.metadata = {
        ...currentMetadata,
        lastError: errorMessage,
        attemptHistory: [
          ...attemptHistory,
          {
            timestamp: new Date().toISOString(),
            error: errorMessage,
          },
        ],
      };

      return this.save();
    }

    /**
     * Find pending notifications ready for processing
     * @param {string} platform - Communication platform
     * @param {number} limit - Max number of records to process
     */
    static async findPendingForProcessing(platform,type, limit = 50) {
      const now = new Date();

      return this.findAll({
        where: {
          platform,
          type,
          status: {
            [Op.in]: ["pending", "retry"],
          },
          expiresAt: {
            [Op.gt]: now,
          },
          [Op.or]: [
            { nextAttemptAt: null },
            { nextAttemptAt: { [Op.lte]: now } },
          ],
          deliveryAttempts: {
            [Op.lt]: sequelize.literal("max_attempts"),
          },
        },
        order: [
          ["priority", "DESC"],
          ["createdAt", "ASC"],
        ],
        limit,
      });
    }

    /**
     * Schedule next delivery attempt with exponential backoff
     */
    async scheduleNextAttempt() {
      const baseDelay = 5; // Base delay in minutes
      const nextAttempt = new Date();

      // Exponential backoff: 5min, 15min, 45min, etc.
      const delayMinutes = baseDelay * Math.pow(3, this.deliveryAttempts - 1);
      nextAttempt.setMinutes(
        nextAttempt.getMinutes() + Math.min(delayMinutes, 1440)
      ); // Max 1 day delay

      this.status = "retry";
      this.nextAttemptAt = nextAttempt;

      return this.save();
    }

    /**
     * Find notifications for analytics/reporting
     * @param {Date} startDate - Start date for report range
     * @param {Date} endDate - End date for report range
     * @param {string} groupBy - Field to group statistics by
     */
    static async getNotificationStats(startDate, endDate, groupBy = "type") {
      const validGroupByFields = ["type", "platform", "status", "priority"];

      if (!validGroupByFields.includes(groupBy)) {
        throw new Error("Invalid groupBy parameter");
      }

      return this.findAll({
        attributes: [
          groupBy,
          [sequelize.fn("COUNT", sequelize.col("id")), "count"],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(
                `CASE WHEN status = 'delivered' THEN 1 ELSE 0 END`
              )
            ),
            "delivered",
          ],
          [
            sequelize.fn(
              "SUM",
              sequelize.literal(`CASE WHEN status = 'failed' THEN 1 ELSE 0 END`)
            ),
            "failed",
          ],
          [
            sequelize.fn("AVG", sequelize.col("deliveryAttempts")),
            "avgAttempts",
          ],
        ],
        where: {
          createdAt: {
            [Op.between]: [startDate, endDate],
          },
        },
        group: [groupBy],
      });
    }
  }

  Notifications.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: true,
        field: "user_id",
      },
      creatorId: {
        type: DataTypes.UUID,
        allowNull: true,
        field: "creator_id",
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
          isEmail: {
            msg: "Must be a valid email address",
          },
        },
      },
      phoneNumber: {
        type: DataTypes.STRING(15),
        allowNull: true,
        field: "phone_number",
      },

      type: {
        type: DataTypes.ENUM(
          "tournament-registration",
          "tournament-withdraw",
          "tournament-pairing",
          "tournament-results",
          "promotional",
          "payment-confirmation",
          "tournament-reminder"
        ),
        allowNull: true,
      },
      platform: {
        type: DataTypes.ENUM("sms", "email", "whatsapp"),
        allowNull: false,
      },
      templateId: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "template_id",
      },
      content: {
        type: DataTypes.JSONB, // Using JSONB for better performance
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM("pending", "retry", "delivered", "failed"),
        defaultValue: "pending",
        allowNull: false,
      },
      priority: {
        type: DataTypes.INTEGER,
        defaultValue: 1,
        allowNull: false,
      },
      expiresAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "expires_at",
      },
      deliveryAttempts: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        allowNull: false,
        field: "delivery_attempts",
      },
      maxAttempts: {
        type: DataTypes.INTEGER,
        defaultValue: 3,
        allowNull: false,
        field: "max_attempts",
      },
      sentAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "sent_at",
      },
      lastAttemptAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "last_attempt_at",
      },
      nextAttemptAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "next_attempt_at",
      },
      failureReason: {
        type: DataTypes.STRING(255),
        allowNull: true,
        field: "failure_reason",
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      // Optional: For tracking batches of related notifications
      batchId: {
        type: DataTypes.UUID,
        allowNull: true,
        field: "batch_id",
      },
      // Optional: For tracking external provider IDs
      externalId: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "external_id",
      },
    },
    {
      sequelize,
      modelName: "Notifications",
      tableName: "notifications",
      timestamps: true,
      paranoid: true, // Soft deletes - keeps records for auditing
      indexes: [
        {
          name: "idx_notifications_status_platform",
          fields: ["status", "platform"],
        },
        {
          name: "idx_notifications_user_id",
          fields: ["user_id"],
        },
        {
          name: "idx_notifications_creator_id",
          fields: ["creator_id"],
        },
        {
          name: "idx_notifications_type",
          fields: ["type"],
        },
        {
          name: "idx_notifications_expires_at",
          fields: ["expires_at"],
        },
        {
          name: "idx_notifications_next_attempt",
          fields: [
            "status",
            "next_attempt_at",
            "delivery_attempts",
            "max_attempts",
          ],
        },
        {
          name: "idx_notifications_batch_id",
          fields: ["batch_id"],
        },
      ],
    }
  );

  return Notifications;
};
