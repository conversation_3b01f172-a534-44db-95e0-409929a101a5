# Notification System

This document describes the notification system implemented in the Chess Brigade API, which includes a cron job for sending SMS notifications for pairing details.

## Overview

The notification system consists of:

1. **Notification Model**: Stores notification records with status tracking
2. **Notification Service**: Handles creating and processing notifications
3. **Cron Service**: Schedules and runs background jobs
4. **API Endpoints**: For creating and managing notifications

## Notification Model

The notification model (`models/notifications.js`) stores:

- User information (userId, phoneNumber, email)
- Notification content and metadata
- Status tracking (pending, delivered, failed, retry)
- Delivery attempts and scheduling

## Notification Service

The notification service (`services/notificationService.js`) provides:

- `createPairingNotifications`: Creates notifications for tournament pairings
- `processPendingSmsNotifications`: Processes pending SMS notifications

## Cron Service

The cron service (`services/cronService.js`) manages:

- Scheduled jobs using node-cron
- Job status tracking
- Manual job execution

By default, it runs a job every 5 minutes to process pending SMS notifications.

## API Endpoints

### Create Pairing Notifications

```
POST /api/v1/notification/pairing
```

**Request Body:**
```json
{
  "tournamentId": "your-tournament-id",
  "round": 1
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully queued and processed X pairing notifications",
  "data": {
    "tournamentId": "your-tournament-id",
    "tournamentTitle": "Tournament Title",
    "totalNotifications": 10,
    "notificationsByRound": {
      "1": 10
    },
    "playerCount": 5
  }
}
```

### Process SMS Notifications Manually

```
POST /api/v1/notification/process-sms
```

**Response:**
```json
{
  "success": true,
  "message": "SMS notifications processed",
  "data": {
    "processed": 10,
    "success": 8,
    "failed": 2
  }
}
```

### Get Notification Status

```
GET /api/v1/notification/status
```

**Query Parameters:**
- `type`: Notification type (e.g., "tournament-pairing")
- `tournamentId`: Tournament ID
- `round`: Round number
- `status`: Notification status (pending, delivered, failed, retry)
- `limit`: Number of records to return (default: 20)
- `offset`: Offset for pagination (default: 0)

**Response:**
```json
{
  "success": true,
  "message": "Notifications retrieved",
  "data": {
    "total": 10,
    "notifications": [
      {
        "id": "uuid",
        "userId": "user-uuid",
        "type": "tournament-pairing",
        "platform": "sms",
        "status": "delivered",
        "deliveryAttempts": 1,
        "maxAttempts": 3,
        "sentAt": "2023-06-01T12:00:00Z",
        "metadata": {
          "tournament_id": "tournament-uuid",
          "round_id": 1,
          "pairing_id": "pairing-uuid"
        },
        "createdAt": "2023-06-01T11:55:00Z",
        "updatedAt": "2023-06-01T12:00:00Z"
      }
    ]
  }
}
```

## Testing

A test script is provided at `tests/notification-test.js` to verify the notification system works correctly.

To run the test:

1. Set environment variables:
   ```
   TEST_TOURNAMENT_ID=your-tournament-id
   TEST_CREATOR_ID=your-user-id
   TEST_ROUND=1
   ```

2. Run the test:
   ```
   node tests/notification-test.js
   ```

## Implementation Details

### Notification Flow

1. When an API endpoint is hit to send pairing notifications:
   - Notifications are created in the database with status "pending"
   - The cron job processes them automatically every 5 minutes
   - Alternatively, they can be processed immediately

2. For failed notifications:
   - The system uses exponential backoff for retries
   - First retry after 5 minutes
   - Second retry after 15 minutes
   - Third retry after 45 minutes, etc.
   - After max attempts, marked as permanently failed

### SMS Integration

The system uses MSG91 for sending SMS messages. The templates must be configured in the application config.

## Configuration

The following environment variables should be set:

```
MSG91_AUTH_KEY=your-auth-key
MSG91_SENDER_ID=your-sender-id
```

Template IDs should be configured in `config/config.js`:

```javascript
msg91: {
  authKey: process.env.MSG91_AUTH_KEY,
  senderId: process.env.MSG91_SENDER_ID || "CHSBRD",
  baseUrl: "https://api.msg91.com/api/v5/flow/",
  templates: {
    pairing_notification_template_id: "your-template-id"
  }
}
```
