
function CheckEligibleCategory(
  tournamentCategory, // 'open' | 'male' | 'female'
  dob,                // e.g., '2005-04-16'
  gender,             // 'male' | 'female' (actual player gender)
  userAgeCategory,    // e.g., 'U18'
  userGenderCategory, // 'male' | 'female' (category player selected)
) {
  const birthDate = new Date(dob);
  const today = new Date();

  let age = today.getFullYear() - birthDate.getFullYear();
  if (
    today.getMonth() < birthDate.getMonth() ||
    (today.getMonth() === birthDate.getMonth() &&
      today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  // Convert category like "U18" into age limit
  const userAgeLimit = userAgeCategory.toUpperCase().startsWith("U")
    ? parseInt(userAgeCategory.substring(1))
    : null;

  // --- Age Validation ---
  if (userAgeLimit !== null && age >= userAgeLimit) {
    return {
      eligible: false,
      reason: `Player age (${age}) exceeds or equals selected age category (${userAgeCategory})`,
    };
  }

  // --- Tournament & Gender Category Validation ---
  if (tournamentCategory === "female") {
    if (gender !== "female" || userGenderCategory !== "female") {
      return {
        eligible: false,
        reason: "Only female players can participate in the 'female' tournament category",
      };
    }
  } else if (tournamentCategory === "male") {
    // Male tournament:
    // Female player can participate but must select male category
    // Male player must select male category
    if (userGenderCategory !== "male") {
      return {
        eligible: false,
        reason: "In male tournaments, players must select the 'male' category",
      };
    }
    // If female player chooses male category - allowed
    // If male player chooses male category - allowed
  } else if (tournamentCategory === "open") {
    // Open tournament:
    // Male player can only select male category
    // Female player can select male or female category
    if (gender === "male" && userGenderCategory !== "male") {
      return {
        eligible: false,
        reason: "Male players in open tournaments can only select 'male' category",
      };
    }
    if (gender === "female" && !["male", "female"].includes(userGenderCategory)) {
      return {
        eligible: false,
        reason: "Female players in open tournaments can select 'male' or 'female' category only",
      };
    }
  } else {
    return {
      eligible: false,
      reason: "Invalid tournament category",
    };
  }

  // All validations passed
  return {
    eligible: true,
    reason: "Player is eligible",
  };
}

module.exports = { CheckEligibleCategory };
