const crypto = require("crypto");
const fs = require("fs");
const path = require("path");
const { config } = require("../config/config");

/**
 * Initialize the Razorpay SDK
 */
let razorpayInstance = null;
try {
  const Razorpay = require("razorpay");
  razorpayInstance = new Razorpay({
    key_id: config.razorpay.key,
    key_secret: config.razorpay.secret,
  });

} catch (error) {
  console.error("Razorpay SDK initialization failed:", error.message);
  throw new Error("Razorpay SDK is required for payment processing");
}

/**
 * Format amount to paise (Razorpay uses smallest currency unit)
 * @param {string|number} amount - Amount in rupees
 * @returns {number} - Amount in paise
 */
const formatAmount = (amount) => {
  const parsed = parseFloat(amount);
  if (isNaN(parsed)) {
    throw new Error("Invalid amount");
  }
  return Math.round(parsed * 100);
};


/**
 * Format amount from paise to rupees
 * @param {number} amountInPaise - Amount in paise
 * @returns {number} - Amount in rupees
 */
const formatAmountFromPaise = (amountInPaise) => {
  return parseFloat((amountInPaise / 100).toFixed(2));
};

/**
 * Create Razorpay order
 * @param {Object} orderData - Order creation data
 * @returns {Promise<Object>} - Created order details
 */
const createOrder = async (orderData) => {
  try {
    const {
      amount,
      currency = "INR",
      receipt,
      notes = {},
      partial_payment = false,
    } = orderData;

    const amountInPaise = formatAmount(amount);


    const orderOptions = {
      amount: amountInPaise,
      currency,
      receipt,
      notes,
      partial_payment,
    };



    const order = await razorpayInstance.orders.create(orderOptions);



    return {
      success: true,
      order,
      formattedAmount: formatAmountFromPaise(order.amount),
    };
  } catch (error) {
    console.error("❌ Failed to create Razorpay order:", error);
    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
};

/**
 * Verify Razorpay payment signature
 * @param {Object} params - Payment verification parameters
 * @returns {boolean} - Whether the signature is valid
 */
const verifyPaymentSignature = (params) => {
  try {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
      params;



    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      console.error(
        "❌ Missing required parameters for signature verification"
      );
      return false;
    }

    // Create the signature string
    const signatureString = `${razorpay_order_id}|${razorpay_payment_id}`;

    // Generate expected signature
    const expectedSignature = crypto
      .createHmac("sha256", config.razorpay.secret)
      .update(signatureString)
      .digest("hex");




    const isValid = expectedSignature === razorpay_signature;

    if (isValid) {

    } else {
      console.error("❌ Payment signature verification failed");
    }

    return isValid;
  } catch (error) {
    console.error("❌ Error during signature verification:", error);
    return false;
  }
};

/**
 * Verify Razorpay webhook signature
 * @param {string} webhookBody - Raw webhook body
 * @param {string} signature - Webhook signature from headers
 * @param {string} webhookSecret - Webhook secret from Razorpay dashboard
 * @returns {boolean} - Whether the webhook signature is valid
 */
const verifyWebhookSignature = (webhookBody, signature, webhookSecret) => {
  try {


    if (!webhookBody || !signature || !webhookSecret) {
      console.error("❌ Missing required parameters for webhook verification");
      return false;
    }

    const expectedSignature = crypto
      .createHmac("sha256", webhookSecret)
      .update(webhookBody)
      .digest("hex");

    const isValid = expectedSignature === signature;

    if (isValid) {

    } else {
      console.error("❌ Webhook signature verification failed");
    }

    return isValid;
  } catch (error) {
    console.error("❌ Error during webhook signature verification:", error);
    return false;
  }
};

/**
 * Fetch payment details from Razorpay
 * @param {string} paymentId - Razorpay payment ID
 * @returns {Promise<Object>} - Payment details
 */
const fetchPaymentDetails = async (paymentId) => {
  try {


    const payment = await razorpayInstance.payments.fetch(paymentId);



    return {
      success: true,
      payment: {
        ...payment,
        formattedAmount: formatAmountFromPaise(payment.amount),
      },
    };
  } catch (error) {
    console.error("❌ Failed to fetch payment details:", error);
    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
};

/**
 * Fetch order details from Razorpay
 * @param {string} orderId - Razorpay order ID
 * @returns {Promise<Object>} - Order details
 */
const fetchOrderDetails = async (orderId) => {
  try {


    const order = await razorpayInstance.orders.fetch(orderId);



    return {
      success: true,
      order: {
        ...order,
        formattedAmount: formatAmountFromPaise(order.amount),
      },
    };
  } catch (error) {
    console.error("❌ Failed to fetch order details:", error);
    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
};

/**
 * Capture payment (for payments that are authorized but not captured)
 * @param {string} paymentId - Razorpay payment ID
 * @param {number} amount - Amount to capture in paise
 * @param {string} currency - Currency code
 * @returns {Promise<Object>} - Capture result
 */
const capturePayment = async (paymentId, amount, currency = "INR") => {
  try {


    const captureResult = await razorpayInstance.payments.capture(
      paymentId,
      amount,
      currency
    );



    return {
      success: true,
      payment: {
        ...captureResult,
        formattedAmount: formatAmountFromPaise(captureResult.amount),
      },
    };
  } catch (error) {
    console.error("❌ Failed to capture payment:", error);
    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
};

/**
 * Create refund for a payment
 * @param {string} paymentId - Razorpay payment ID
 * @param {Object} refundData - Refund details
 * @returns {Promise<Object>} - Refund result
 */
const createRefund = async (paymentId, refundData = {}) => {
  try {
    const { amount, notes = {}, receipt, speed = "normal" } = refundData;



    const refundOptions = {
      ...(amount && { amount: formatAmount(amount) }),
      notes,
      ...(receipt && { receipt }),
      speed,
    };

    const refund = await razorpayInstance.payments.refund(
      paymentId,
      refundOptions
    );



    return {
      success: true,
      refund: {
        ...refund,
        formattedAmount: formatAmountFromPaise(refund.amount),
      },
    };
  } catch (error) {
    console.error("❌ Failed to create refund:", error);
    return {
      success: false,
      error: error.message,
      details: error,
    };
  }
};

/**
 * Generate checkout options for frontend integration
 * @param {Object} checkoutData - Checkout configuration
 * @returns {Object} - Razorpay checkout options
 */
const generateCheckoutOptions = (checkoutData) => {
  const {
    order_id,
    amount,
    currency = "INR",
    name,
    description,
    image,
    prefill = {},
    notes = {},
    theme = {},
    modal = {},
    callback_url,
    redirect = false,
  } = checkoutData;

  const checkoutOptions = {
    key: config.razorpay.key,
    order_id,
    amount: formatAmount(amount),
    currency,
    name,
    description,
    image,
    prefill: {
      name: prefill.name || "",
      email: prefill.email || "",
      contact: prefill.contact || "",
    },
    notes,
    theme: {
      color: theme.color || "#3399cc",
    },
    modal: {
      ondismiss:
        modal.ondismiss ||
        function () {

        },
    },
    ...(callback_url && { callback_url }),
    redirect,
  };



  return checkoutOptions;
};

/**
 * Process webhook event
 * @param {Object} webhookEvent - Webhook event data
 * @returns {Object} - Processed event information
 */
const processWebhookEvent = (webhookEvent) => {
  try {
    const { event, payload } = webhookEvent;



    const processedEvent = {
      event,
      entity:
        payload.payment?.entity ||
        payload.order?.entity ||
        payload.refund?.entity,
      timestamp: new Date().toISOString(),
    };

    // Add formatted amounts where applicable
    if (payload.payment?.entity?.amount) {
      processedEvent.formattedAmount = formatAmountFromPaise(
        payload.payment.entity.amount
      );
    }

    if (payload.order?.entity?.amount) {
      processedEvent.formattedAmount = formatAmountFromPaise(
        payload.order.entity.amount
      );
    }

    if (payload.refund?.entity?.amount) {
      processedEvent.formattedAmount = formatAmountFromPaise(
        payload.refund.entity.amount
      );
    }



    return {
      success: true,
      processedEvent,
      originalPayload: webhookEvent,
    };
  } catch (error) {
    console.error("❌ Failed to process webhook event:", error);
    return {
      success: false,
      error: error.message,
      originalPayload: webhookEvent,
    };
  }
};

/**
 * Log Razorpay transaction details
 * @param {Object} transactionData - Transaction data to log
 * @param {string} logType - Type of log (order, payment, refund, etc.)
 */
const logTransaction = (transactionData, logType = "transaction") => {
  try {
    const logDir = path.join(__dirname, "../logs");
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    const logFile = path.join(logDir, "razorpay_transactions.log");
    const logData =
      JSON.stringify(
        {
          timestamp: new Date().toISOString(),
          type: logType,
          data: transactionData,
        },
        null,
        2
      ) + "\n\n";

    fs.appendFileSync(logFile, logData);

  } catch (err) {
    console.error("Failed to log transaction:", err);
  }
};

/**
 * Utility function to get payment status in a standardized format
 * @param {string} razorpayStatus - Razorpay payment status
 * @returns {string} - Standardized status
 */
const getStandardizedStatus = (razorpayStatus) => {
  const statusMap = {
    created: "pending",
    authorized: "authorized",
    captured: "success",
    refunded: "refunded",
    failed: "failed",
  };

  return statusMap[razorpayStatus] || razorpayStatus;
};

module.exports = {
  // Core payment functions
  createOrder,
  verifyPaymentSignature,
  verifyWebhookSignature,

  // Data fetching functions
  fetchPaymentDetails,
  fetchOrderDetails,

  // Payment operations
  capturePayment,
  createRefund,

  // Frontend integration
  generateCheckoutOptions,

  // Webhook handling
  processWebhookEvent,

  // Utilities
  formatAmount,
  formatAmountFromPaise,
  getStandardizedStatus,
  logTransaction,

  // Direct access to Razorpay instance for advanced usage
  razorpayInstance,
};
