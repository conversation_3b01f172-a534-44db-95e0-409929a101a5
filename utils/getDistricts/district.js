const districtList = require("./assets/districts.json");
const { compare } = require("./utils");

const KEYS = ["name", "stateCode", "countryCode"];

let convertedDistrictList = [];

/**
 * Get a list of all districts.
 * @param {Array} keys - The keys to include in the returned objects
 * @returns {Array} - Array of district objects
 */
function getAllDistricts() {
  // Check if the list is already populated
  if (convertedDistrictList.length) {
    return convertedDistrictList;
  }

  const districtJSON = districtList;

  // You can directly assign the list or modify it further if needed
  convertedDistrictList = districtJSON;

  return convertedDistrictList;
}

/**
 * Get a list of districts belonging to a specific state and country.
 * @param {string} countryCode - The country code (e.g., 'IN')
 * @param {string} stateCode - The state code (e.g., 'WB')
 * @returns {Array} - Array of district objects sorted by name
 */
function getDistrictsOfState(stateCode) {
  if (!stateCode) return [];

  const countryCode = "IN";

  // Get all districts, assuming getAllDistricts returns the list of districts
  const districts = getAllDistricts();

  // Filter the districts based on countryCode and stateCode
  const filteredDistricts = districts.filter((district) => {
    return (
      district.countryCode === countryCode && district.stateCode === stateCode
    );
  });

  // Sort the filtered districts by their name
  return filteredDistricts.sort(compare);
}

/**
 * Get a list of districts belonging to a specific country.
 * @param {string} countryCode - The country code (e.g., 'IN')
 * @returns {Array} - Array of district objects sorted by state and name
 */
function getDistrictsOfCountry(countryCode) {
  if (!countryCode) return [];

  const districts = getAllDistricts();
  const filteredDistricts = districts.filter((value) => {
    return value.countryCode === countryCode;
  });

  return sortByStateAndName(filteredDistricts);
}

/**
 * Sort districts by state code and then by name.
 * @param {Array} districts - Array of district objects
 * @returns {Array} - Sorted array of district objects
 */
function sortByStateAndName(districts) {
  return districts.sort((a, b) => {
    const result = compare(a, b, (entity) => {
      return `${entity.countryCode}-${entity.stateCode}`;
    });

    if (result !== 0) return result;
    return compare(a, b);
  });
}

/**
 * Get a district by its name, state code, and country code.
 * @param {string} name - The district name
 * @param {string} stateCode - The state code
 * @param {string} countryCode - The country code
 * @returns {Object|null} - The district object or null if not found
 */
function getDistrictByName(name, stateCode, countryCode) {
  if (!name || !stateCode || !countryCode) return null;

  const districts = getAllDistricts();
  return (
    districts.find(
      (district) =>
        district.name === name &&
        district.stateCode === stateCode &&
        district.countryCode === countryCode
    ) || null
  );
}

module.exports = {
  getAllDistricts,
  getDistrictsOfState,
  getDistrictsOfCountry,
  getDistrictByName,
  sortByStateAndName,
};
