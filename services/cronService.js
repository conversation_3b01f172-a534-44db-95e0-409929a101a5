const cron = require("node-cron");
const notificationService = require("./notificationService");

// Store active cron jobs
const activeCronJobs = {};

/**
 * Service for managing cron jobs
 */
const cronService = {
  /**
   * Initialize all cron jobs
   */
  initCronJobs: () => {
    // Process email notifications every 2 minutes
    cronService.startJob(
      "process-email-notifications",
      "*/2 * * * *", // Run every 2 minutes
      async () => {
        console.log("Running cron job: process-email-notifications");
        try {
          const result =
            await notificationService.processPendingEmailNotifications();
          console.log(
            `Processed ${result.total} email notifications: ${result.success} succeeded, ${result.failed} failed`
          );
        } catch (error) {
          console.error("Error processing email notifications:", error);
        }
      }
    );

    // Process SMS notifications every 5 minutes
    cronService.startJob(
      "process-sms-notifications",
      "*/5 * * * *", // Run every 5 minutes
      async () => {
        console.log("Running cron job: process-sms-notifications");
        try {
          const result =
            await notificationService.processPendingSmsNotifications();
          console.log(
            `Processed ${result.total} SMS notifications: ${result.success} succeeded, ${result.failed} failed`
          );
        } catch (error) {
          console.error("Error processing SMS notifications:", error);
        }
      }
    );

    cronService.startJob(
      "tournament-email-reminders",
      "*/30 * * * *",
      async () => {
        console.log("Running cron job: tournament-email-reminders");
        try {
          const result =
            await notificationService.processTournamentEmailReminders();
          console.log(
            `Email reminders: ${result.success} sent, ${result.failed} failed`
          );
        } catch (error) {
          console.error("Error in tournament email reminders job:", error);
        }
      }
    );
  },

  /**
   * Start a cron job
   * @param {string} jobName - Name of the job
   * @param {string} schedule - Cron schedule expression
   * @param {Function} task - Function to execute
   * @returns {boolean} - Whether the job was started
   */
  startJob: (jobName, schedule, task) => {
    // Stop the job if it's already running
    cronService.stopJob(jobName);

    try {
      // Validate the cron expression
      if (!cron.validate(schedule)) {
        console.error(`Invalid cron schedule for job ${jobName}: ${schedule}`);
        return false;
      }

      // Schedule the job
      const job = cron.schedule(schedule, task, {
        scheduled: true,
        timezone: "Asia/Kolkata", // Use Indian timezone
      });

      // Store the job
      activeCronJobs[jobName] = job;
      console.log(`Cron job started: ${jobName} (${schedule})`);
      return true;
    } catch (error) {
      console.error(`Error starting cron job ${jobName}:`, error);
      return false;
    }
  },

  /**
   * Stop a cron job
   * @param {string} jobName - Name of the job
   * @returns {boolean} - Whether the job was stopped
   */
  stopJob: (jobName) => {
    if (activeCronJobs[jobName]) {
      activeCronJobs[jobName].stop();
      delete activeCronJobs[jobName];
      console.log(`Cron job stopped: ${jobName}`);
      return true;
    }
    return false;
  },

  /**
   * Run a job immediately
   * @param {string} jobName - Name of the job
   * @returns {Promise<any>} - Result of the job
   */
  runJobNow: async (jobName) => {
    switch (jobName) {
      case "process-promotional-email-notifications":
        return await notificationService.processPendingPromotionalEmailNotifications();
      case "process-sms-notifications":
        return await notificationService.processPendingSmsNotifications();
      case "tournament-email-reminders":
        return await notificationService.processTournamentEmailReminders();
      default:
        throw new Error(`Unknown job: ${jobName}`);
    }
  },

  /**
   * Get all active cron jobs
   * @returns {Object} - Map of job names to their status
   */
  getActiveJobs: () => {
    const jobs = {};
    for (const [name, job] of Object.entries(activeCronJobs)) {
      jobs[name] = {
        status: job.getStatus(),
        name,
      };
    }
    return jobs;
  },
};

module.exports = cronService;
