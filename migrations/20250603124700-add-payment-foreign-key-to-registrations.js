'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add foreign key constraint for payment_id in registrations table
    await queryInterface.addConstraint('registrations', {
      fields: ['payment_id'],
      type: 'foreign key',
      name: 'fk_registrations_payment_id',
      references: {
        table: 'payments',
        field: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove the foreign key constraint
    await queryInterface.removeConstraint('registrations', 'fk_registrations_payment_id');
  }
};
