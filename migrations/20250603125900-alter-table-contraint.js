
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add foreign key constraints for payments table
    await queryInterface.addConstraint("payments", {
      fields: ["user_id"],
      type: "foreign key",
      name: "fk_payments_user_id",
      references: {
        table: "users",
        field: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "RESTRICT",
    });

    await queryInterface.addConstraint("payments", {
      fields: ["tournament_id"],
      type: "foreign key",
      name: "fk_payments_tournament_id",
      references: {
        table: "tournament",
        field: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "RESTRICT",
    });

    await queryInterface.addConstraint("payments", {
      fields: ["registration_id"],
      type: "foreign key",
      name: "fk_payments_registration_id",
      references: {
        table: "registrations",
        field: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "SET NULL",
    });

    await queryInterface.addConstraint("payments", {
      fields: ["bulk_registration_id"],
      type: "foreign key",
      name: "fk_payments_bulk_registration_id",
      references: {
        table: "bulk_registrations",
        field: "id",
      },
      onUpdate: "CASCADE",
      onDelete: "SET NULL",
    });

    // Add foreign key constraints for registrations table
    await queryInterface.addConstraint('registrations', {
      fields: ['tournament_id'],
      type: 'foreign key',
      name: 'fk_registrations_tournament_id',
      references: {
        table: 'tournament',
        field: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    await queryInterface.addConstraint('registrations', {
      fields: ['player_id'],
      type: 'foreign key',
      name: 'fk_registrations_player_id',
      references: {
        table: 'users',
        field: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    await queryInterface.addConstraint('registrations', {
      fields: ['payment_id'],
      type: 'foreign key',
      name: 'fk_registrations_payment_id',
      references: {
        table: 'payments',
        field: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove all foreign key constraints
    await queryInterface.removeConstraint("payments", "fk_payments_user_id");
    await queryInterface.removeConstraint("payments", "fk_payments_tournament_id");
    await queryInterface.removeConstraint("payments", "fk_payments_registration_id");
    await queryInterface.removeConstraint("payments", "fk_payments_bulk_registration_id");
    
    await queryInterface.removeConstraint('registrations', 'fk_registrations_tournament_id');
    await queryInterface.removeConstraint('registrations', 'fk_registrations_player_id');
    await queryInterface.removeConstraint('registrations', 'fk_registrations_payment_id');
  }
};