'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Get all table names that have timestamp columns
    const tables = [
      'users',
      'tournament', 
      'club_details',
      'registrations',
      'arbiter_details',
      'bank_details',
      'bulk_registrations',
      'contents',
      'invite_requests',
      'notifications',
      'otp',
      'pairings',
      'payments',
      'players',
      'rankings',
      'certificates',
      'refunds',
      'advertisements'
    ];

    // Rename timestamp columns for each table
    for (const tableName of tables) {
      try {
        // Check if the table exists and has the camelCase columns
        const tableInfo = await queryInterface.describeTable(tableName);
        
        if (tableInfo.createdAt) {
          await queryInterface.renameColumn(tableName, 'createdAt', 'created_at');
        }
        
        if (tableInfo.updatedAt) {
          await queryInterface.renameColumn(tableName, 'updatedAt', 'updated_at');
        }
        
        if (tableInfo.deletedAt) {
          await queryInterface.renameColumn(tableName, 'deletedAt', 'deleted_at');
        }
      } catch (error) {
        // Table might not exist or columns might already be renamed
        console.log(`Skipping ${tableName}: ${error.message}`);
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Reverse the operation - rename back to camelCase
    const tables = [
      'users',
      'tournament', 
      'club_details',
      'registrations',
      'arbiter_details',
      'bank_details',
      'bulk_registrations',
      'contents',
      'invite_requests',
      'notifications',
      'otp',
      'pairings',
      'payments',
      'players',
      'rankings',
      'certificates',
      'refunds',
      'advertisements'
    ];

    for (const tableName of tables) {
      try {
        const tableInfo = await queryInterface.describeTable(tableName);
        
        if (tableInfo.created_at) {
          await queryInterface.renameColumn(tableName, 'created_at', 'createdAt');
        }
        
        if (tableInfo.updated_at) {
          await queryInterface.renameColumn(tableName, 'updated_at', 'updatedAt');
        }
        
        if (tableInfo.deleted_at) {
          await queryInterface.renameColumn(tableName, 'deleted_at', 'deletedAt');
        }
      } catch (error) {
        console.log(`Skipping ${tableName}: ${error.message}`);
      }
    }
  }
};
