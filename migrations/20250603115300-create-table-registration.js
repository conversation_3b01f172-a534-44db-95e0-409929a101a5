'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create ENUM type first
    await queryInterface.sequelize.query(
      `DO $$ BEGIN
        CREATE TYPE "enum_registrations_status" AS ENUM ('active', 'cancelled', 'refunded');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;`
    );

    await queryInterface.createTable('registrations', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v4()'),
        allowNull: false,
        primaryKey: true,
      },
      tournament_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'tournament', // Matches model reference
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      reg_id: {
        type: Sequelize.STRING,
        unique: true,
        allowNull: true,
      },
      player_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      registered_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        defaultValue: Sequelize.fn('NOW'),
      },
      age_category: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      gender_category: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      tournament_title: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      registration_remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      payment_id: {
        type: Sequelize.UUID, // Changed from STRING to UUID to match model
        allowNull: true,
        // Note: Foreign key constraint will be added after payments table is created
      },
      attendance_mark: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      payment_amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      payment_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      status: {
        type: "enum_registrations_status",
        allowNull: false,
        defaultValue: "active",
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
    });

    // Create the unique index
    await queryInterface.addIndex('registrations', ['tournament_id', 'player_id'], {
      unique: true,
      name: 'registrations_tournament_player_unique_index',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('registrations');
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_registrations_status";`
    );
  }
};