"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create enum type for result field
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_pairings_result" AS ENUM ('white', 'black', 'draw');`
    );

    await queryInterface.createTable("pairings", {
      id: {
        type: Sequelize.UUID,
        allowNull: false,
        primaryKey: true,
        defaultValue: Sequelize.UUIDV4,
      },
      tournament_id: {
        type: Sequelize.UUID,
        allowNull: false,
      },
      round_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      age_category: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      gender_category: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      board_no: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      white_player_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      white_player_ranking: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      white_player_fide_rating: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      white_player_points: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      black_player_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      black_player_ranking: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      black_player_fide_rating: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      black_player_points: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      result: {
        type: "enum_pairings_result",
        allowNull: false,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn("NOW"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn("NOW"),
      },
    });

    // Add foreign key for tournament_id
    await queryInterface.addConstraint("pairings", {
      fields: ["tournament_id"],
      type: "foreign key",
      name: "fk_pairings_tournament",
      references: {
        table: "tournament",
        field: "id",
      },
      onDelete: "cascade",
      onUpdate: "cascade",
    });

    // Note: The foreign keys white_player_name and black_player_name referencing User.name 
    // are not enforced by constraints because they use 'constraints: false' in the model.
    // If you want, you can manually add constraints, but it's uncommon for string keys.
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable("pairings");
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_pairings_result";`
    );
  },
};
