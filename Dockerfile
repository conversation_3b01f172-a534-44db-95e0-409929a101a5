FROM node:18-alpine AS build

WORKDIR /chessbrigade-ui

# Copy only package files first to leverage Docker layer caching
COPY package*.json ./

# Install all dependencies, including devDependencies
RUN npm install

# Copy the rest of the application code
COPY . .
ARG VITE_API_URL
ENV VITE_API_URL=$VITE_API_URL

# Build the application
RUN npm run build

# Use a multi-stage build for a smaller final image

FROM nginx:alpine

# Copy the custom Nginx config
COPY ./nginx.conf /etc/nginx/conf.d/default.conf

COPY --from=build /chessbrigade-ui/dist /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]