// middlewares/checkAccess.js
const { User } =require("../config/db").models;

const checkAccess = async (req, res, next) => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      return res.status(401).json({ success: false, error: "Unauthorized Entry" });
    }

    const user = await User.findOne({where:{id:userId}});
    if (!user || !user.isAccess) {
      return res.status(403).json({ success: false, error: "Access denied. Please contact support." });
    }

    next();
  } catch (err) {
    return res.status(500).json({ success: false, error: "Internal server error" });
  }
};

module.exports = checkAccess;
