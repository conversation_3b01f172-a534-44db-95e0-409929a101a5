const verifyRole = (roles) => {
  return (req, res, next) => {
    const userRole = req.user?.role;

    if (!userRole) {
      return res
        .status(401)
        .json({ message: "Unauthorized: User role not found" });
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (allowedRoles.includes(userRole) || userRole === "admin") {
      return next();
    }

    return res
      .status(403)
      .json({ message: "Forbidden: Insufficient permissions" });
  };
};

module.exports = verifyRole;
