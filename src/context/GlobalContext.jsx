import React, { createContext, useState, useEffect, useCallback } from "react";
import { Client, updateToken } from "../api/client";
import { decrypt, encrypt } from "../utils/encode";

// Only define a shape for validation - keeping JavaScript
const userShape = {
  userId: "",
  name: "",
  email: "",
  role: "",
};

export const GlobalContext = createContext();

const GlobalContextProvider = ({ children }) => {
  // Store only token in localStorage
  const [token, setToken] = useState(() => {
    // Initialize token from localStorage on component mount
    const storedToken = localStorage.getItem("jwt_token");
    // Ensure token is set in the API client on initial load
    if (storedToken) {
      updateToken(storedToken);
    }
    return storedToken;
  });

  // User object will be fetched from the backend on each session
  const [user, setUser] = useState(() => {
    const stored = localStorage.getItem("app_config");
    if (stored) {
      try {
        const decryptedUser = decrypt(stored);
        // Validate the decrypted user structure
        const isValid = Object.keys(userShape).every(
          (key) => key in decryptedUser
        );
        if (isValid) {
          return decryptedUser;
        } else {
          localStorage.removeItem("app_config");
          return null;
        }
      } catch (error) {
        console.warn("Failed to decrypt user data:", error);
        localStorage.removeItem("app_config");
        return null;
      }
    }
    return null;
  });

  // Single profile state instead of role-specific states
  const [currentProfile, setCurrentProfile] = useState(null);

  // Add loading state to prevent premature rendering
  const [authLoading, setAuthLoading] = useState(
    !!localStorage.getItem("jwt_token") && !user
  );

  const [openModel, setOpenModel] = useState({
    login: false,
    signup: false,
    forgotpassword: false,
  });

  const isLoggedIn = !!user && !!token;

  const updateUser = useCallback((newUser) => {
    if (newUser && typeof newUser === "object") {
      const isValid = Object.keys(userShape).every((key) => key in newUser);
      if (!isValid) {
        console.warn("Invalid user object structure");
        return;
      }
      setUser(newUser);
      try {
        const encrypted = encrypt(newUser);
        localStorage.setItem("app_config", encrypted);
      } catch (error) {
        console.error("Failed to encrypt user data:", error);
      }
    } else if (newUser === null) {
      setUser(null);
      localStorage.removeItem("app_config");
    } else {
      console.warn("Invalid user data type");
    }
  }, []);

  const setTokenWithStorage = useCallback((newToken) => {
    if (newToken) {
      localStorage.setItem("jwt_token", newToken);
    } else {
      localStorage.removeItem("jwt_token");
    }
    setToken(newToken);
  }, []);

  // Cross-tab communication
  useEffect(() => {
    const channel = new BroadcastChannel("app_state_channel");

    channel.onmessage = (event) => {
      if (event.data.type === "LOGOUT") {
        updateUser(null);
        setTokenWithStorage(null);
        setCurrentProfile(null);
        setAuthLoading(false);
      } else if (event.data.type === "LOGIN") {
        // Set loading true while we fetch user data
        setAuthLoading(true);
        setTokenWithStorage(event.data.token);
      }
    };

    return () => {
      channel.close();
    };
  }, [updateUser, setTokenWithStorage]);

  useEffect(() => {
    updateToken(token);
  }, [token]);

  // Broadcast logout to other tabs
  const broadcastLogout = useCallback(() => {
    const channel = new BroadcastChannel("app_state_channel");
    channel.postMessage({ type: "LOGOUT" });
    channel.close();
  }, []);

  // Handle logout
// Handle logout
useEffect(() => {
  const handleLogout = () => {
    setAuthLoading(false);
    updateUser(null);              // Clears user state & localStorage
    setTokenWithStorage(null);     // Clears token state & localStorage  
    setCurrentProfile(null);       // Clears profile state
    sessionStorage.removeItem("profile_data");
    sessionStorage.removeItem("user_validated");
    broadcastLogout();            // Notifies other tabs
  };

  window.addEventListener("logout", handleLogout);
  return () => {
    window.removeEventListener("logout", handleLogout);
  };
}, [broadcastLogout]);

  // Broadcast login to other tabs
  const broadcastLogin = useCallback((userData, newToken) => {
    const channel = new BroadcastChannel("app_state_channel");
    // Only broadcast the token, not the user object
    channel.postMessage({ type: "LOGIN", token: newToken });
    channel.close();
  }, []);

  // Fetch user data on token change or initial load
  useEffect(() => {
    const fetchUserData = async () => {
      // Always check localStorage directly to ensure we catch tokens on first mount
      const storedToken = localStorage.getItem("jwt_token");

      if (!storedToken) {
        setAuthLoading(false);
        return;
      }

      if (user && storedToken === token) {
        setAuthLoading(false);
        return;
      }

      // Set loading to true while fetching
      setAuthLoading(true);

      // Ensure token is set in the client
      updateToken(storedToken);

      try {
        const res = await Client.get("/auth/me");

        if (res.data?.success && res.data?.data) {
          const backendUser = res.data.data;
          const userData = {
            userId: backendUser.id,
            name: backendUser.name,
            email: backendUser?.email,
            role: backendUser.role,
          };

          updateUser(userData);

          // Make sure token state is updated if we're using the localStorage token
          if (storedToken !== token) {
            setToken(storedToken);
          }
        } else {
          // Invalid response - clear auth state
          setTokenWithStorage(null);
          updateUser(null);
        }
      } catch (err) {
        console.error("Failed to fetch user data:", err);
        setTokenWithStorage(null);
        updateUser(null);
      } finally {
        // Always set loading to false when done
        setAuthLoading(false);
      }
    };

    // This will run on initial mount and whenever token changes
    fetchUserData();
  }, [token]);

  // Fetch profile based on current user role from backend
  const fetchProfileData = async () => {
    if (!user || !token) return null;

    try {
      let endpoint = null;

      // Determine endpoint based on user role
      switch (user.role) {
        case "player":
          endpoint = "/player/profile";
          break;
        case "club":
          endpoint = "/club/profile";
          break;
        case "arbiter":
          endpoint = "/arbiter/profile";
          break;
        default:
          console.error("Unknown role:", user.role);
          return null;
      }

      // Always fetch fresh data from backend
      const res = await Client.get(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (res.status === 204) {
        setCurrentProfile(null);
        return null;
      }

      if (res.data.success) {
        const profileData = res.data.data;
        setCurrentProfile(profileData);
        // We can cache in sessionStorage for performance, but with role check
        sessionStorage.setItem(
          "profile_data",
          JSON.stringify({
            role: user.role,
            data: profileData,
          })
        );
        return profileData;
      }

      return null;
    } catch (error) {
      console.error("Error fetching profile data:", error);
      return null;
    }
  };

  // Load profile from sessionStorage if available and role matches
  useEffect(() => {
    if (!user) {
      setCurrentProfile(null);
      return;
    }

    const cachedProfileData = sessionStorage.getItem("profile_data");
    if (cachedProfileData) {
      try {
        const parsed = JSON.parse(cachedProfileData);
        // Only use cached data if it matches current user role
        if (parsed.role === user.role) {
          setCurrentProfile(parsed.data);
        } else {
          // Role mismatch - clear cached data
          sessionStorage.removeItem("profile_data");
          // Fetch fresh data
          fetchProfileData();
        }
      } catch (err) {
        console.error("Error parsing cached profile:", err);
        sessionStorage.removeItem("profile_data");
      }
    } else {
      // No cached data - fetch fresh
      fetchProfileData();
    }
  }, [user]);

  const removeProfileData = () => {
    setCurrentProfile(null);
    sessionStorage.removeItem("profile_data");
  };
  const clearAuthState = useCallback(() => {
    setAuthLoading(false);
    updateUser(null);
    setTokenWithStorage(null);
    setCurrentProfile(null);
    sessionStorage.removeItem("profile_data");
    sessionStorage.removeItem("user_validated");
    sessionStorage.removeItem("new_user");
    broadcastLogout();
  }, [updateUser, setTokenWithStorage, broadcastLogout]);

  return (
    <GlobalContext.Provider
      value={{
        user,
        isLoggedIn,
        updateUser,
        token,
        setToken: setTokenWithStorage,
        broadcastLogout,
        broadcastLogin,
        openModel,
        setOpenModel,
        currentProfile,
        fetchProfileData,
        removeProfileData,
        // Expose loading state to app
        authLoading,
        clearAuthState,
      }}
    >
      {children}
    </GlobalContext.Provider>
  );
};

export default GlobalContextProvider;
