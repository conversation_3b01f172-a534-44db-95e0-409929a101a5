import { Client } from "../../api/client";

// Flag to prevent multiple simultaneous bulk payments
let isBulkProcessing = false;

/**
 * Creates a bulk payment order on the backend
 * @param {Object} params - Bulk payment parameters
 */
export const createBulkOrder = async (params) => {
  const { bulkRegistrationId } = params;

  try {
    const response = await Client.post("/payment/create-bulk-order", {
      bulkRegistrationId,
    });

    if (!response.data?.success) {
      throw new Error(response.data?.message || "Bulk order creation failed");
    }

    return response.data.data;
  } catch (error) {
    console.error("Bulk order creation error:", error);
    throw error;
  }
};

/**
 * Verifies bulk payment after successful Razorpay transaction
 * @param {Object} razorpayResponse - Response from Razorpay
 */
export const verifyBulkPayment = async (razorpayResponse) => {
  try {
    const response = await Client.post("/payment/verify-bulk-payment", {
      razorpay_payment_id: razorpayResponse.razorpay_payment_id,
      razorpay_order_id: razorpayResponse.razorpay_order_id,
      razorpay_signature: razorpayResponse.razorpay_signature,
    });

    if (!response.data?.success) {
      throw new Error(response.data?.message || "Bulk payment verification failed");
    }

    return response.data.data;
  } catch (error) {
    console.error("Bulk payment verification error:", error);
    throw error;
  }
};

/**
 * Extracts user-friendly error message for bulk payments
 * @param {Error} error - Error object
 * @returns {string} - User-friendly error message
 */
export const getBulkErrorMessage = (error) => {
  if (error.response?.data?.error) {
    return error.response.data.error;
  }
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return "An error occurred while processing bulk payment";
};

/**
 * Main bulk payment initiation function
 * @param {Object} params - Bulk payment parameters and callbacks
 * @param {Function} toast - Toast notification function
 */
export const initiateBulkPayment = async (params, toast) => {
  const {
    bulkRegistrationId,
    onStart,
    onSuccess,
    onError,
    onFinally,
  } = params;
  const razorpayKey = import.meta.env.VITE_RAZORPAY_KEY;

  if (isBulkProcessing) {
    toast.warn("Bulk payment is already in progress");
    return;
  }

  isBulkProcessing = true;
  onStart?.();

  try {
    // Step 1: Create bulk order
    const orderData = await createBulkOrder({
      bulkRegistrationId,
    });


    // Step 2: Open Razorpay checkout for bulk payment
    const options = {
      key: razorpayKey,
      amount: orderData?.checkoutOptions?.amount,
      currency: orderData?.checkoutOptions?.currency || "INR",
      name: orderData?.checkoutOptions?.name || "Bulk Tournament Registration",
      description: orderData?.checkoutOptions?.description,
      order_id: orderData?.orderId,
      prefill: {
        name: orderData?.checkoutOptions?.prefill?.name,
        email: orderData?.checkoutOptions?.prefill?.email,
        contact: orderData?.checkoutOptions?.prefill?.contact,
      },
      theme: {
        color: "#3399cc",
      },
      handler: async (response) => {
        try {
          // Step 3: Verify bulk payment

          const verificationResult = await verifyBulkPayment(response);
          onSuccess?.(verificationResult);

          toast.success("Bulk payment successful!");
        } catch (error) {
          onError?.(error);
          toast.error("Bulk payment verification failed");
        }
      },
      modal: {
        ondismiss: () => {

          isBulkProcessing = false;
          onFinally?.();
        },
      },
      notes: {
        paymentType: "bulk",
        bulkRegistrationId: bulkRegistrationId,
        playerCount: orderData?.playerCount,
      },
    };

    const rzp = new window.Razorpay(options);
    rzp.open();

    // Return order data for additional processing if needed
    return {
      orderId: orderData?.orderId,
      amount: orderData?.amount,
      tournamentTitle: orderData?.tournamentTitle,
      playerCount: orderData?.playerCount,
    };

  } catch (error) {
    const errorMessage = getBulkErrorMessage(error);
    toast.error(errorMessage);
    onError?.(error);
    console.error("Bulk payment initiation error:", error);
  } finally {
    isBulkProcessing = false;
    onFinally?.();
  }
};