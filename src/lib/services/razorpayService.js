import { Client } from "../../api/client";

// Global state to prevent multiple simultaneous payments
let isProcessing = false;

/**
 * Creates Razorpay order - replaces PayU initiate endpoint
 * @param {Object} params - Payment parameters
 */
export const createOrder = async (params) => {
  const { tournamentId, ageCategory, genderCategory } = params;

  try {
    const response = await Client.post("/payment/create-order", {
      tournamentId,
      action: "tournament_registration",
      ageCategory,
      genderCategory,
      registrationType: "player",
      registerAfterPayment: true,
    });

    if (!response.data?.success) {
      throw new Error(response.data?.message || "Order creation failed");
    }

    return response.data.data;
  } catch (error) {
    console.error("Order creation error:", error);
    throw error;
  }
};

/**
 * Verifies payment after successful Razorpay transaction
 * @param {Object} razorpayResponse - Response from Razorpay
 */
export const verifyPayment = async (razorpayResponse) => {
  try {
    const response = await Client.post("/payment/verify-payment", {
      razorpay_payment_id: razorpayResponse.razorpay_payment_id,
      razorpay_order_id: razorpayResponse.razorpay_order_id,
      razorpay_signature: razorpayResponse.razorpay_signature,
    });

    if (!response.data?.success) {
      throw new Error(response.data?.message || "Payment verification failed");
    }

    return response.data.data;
  } catch (error) {
    console.error("Payment verification error:", error);
    throw error;
  }
};

/**
 * Extracts user-friendly error message
 * @param {Error} error - Error object
 * @returns {string} - User-friendly error message
 */
export const getErrorMessage = (error) => {
  if (error.response?.data?.error) {
    return error.response.data.error;
  }
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return "An error occurred while processing payment";
};

/**
 * Main payment initiation function - replaces your old PayU initiatePayment function
 * @param {Object} params - Payment parameters and callbacks
 */
export const initiatePayment = async (params, toast) => {
  const {
    tournamentId,
    ageCategory,
    genderCategory,
    onStart,
    onSuccess,
    onError,
    onFinally,
  } = params;
  const razorpayKey = import.meta.env.VITE_RAZORPAY_KEY;

  if (isProcessing) {
    toast.warn("Payment is already in progress");
    return;
  }

  isProcessing = true;
  onStart?.();

  try {
    // Step 1: Create order (replaces PayU form data)
    const orderData = await createOrder({
      tournamentId,
      ageCategory,
      genderCategory,
    });


    // Step 2: Open Razorpay checkout
    const options = {
      key: razorpayKey,
      amount: orderData?.checkoutOptions?.amount,
      currency: orderData?.checkoutOptions?.currency || "INR",
      name: orderData?.checkoutOptions?.name || "Tournament Registration",
      description: orderData?.checkoutOptions?.description,
      order_id: orderData?.orderId,
      prefill: {
        name: orderData?.checkoutOptions?.prefill?.name,
        email: orderData?.checkoutOptions?.prefill?.email,
        contact: orderData?.checkoutOptions?.prefill?.contact,
      },
      theme: {
        color: "#3399cc",
      },
      handler: async (response) => {
        try {
          // Step 3: Verify payment (replaces PayU success callback)

          const verificationResult = await verifyPayment(response);
          onSuccess?.(verificationResult);

          toast.success("Payment successful!");
        } catch (error) {
          onError?.(error);
          toast.error("Payment verification failed");
        }
      },
      modal: {
        ondismiss: () => {

          isProcessing = false;
          onFinally?.();
        },
      },
    };

    const rzp = new window.Razorpay(options);
    rzp.open();
  } catch (error) {
    const errorMessage = getErrorMessage(error);
    toast.error(errorMessage);
    onError?.(error);
    console.error("Payment initiation error:", error);
  } finally {
    isProcessing = false;
    onFinally?.();
  }
};

/**
 * Utility function to check if payment is currently processing
 * @returns {boolean} - Whether payment is in progress
 */
export const getIsProcessing = () => isProcessing;

/**
 * Utility function to reset processing state (use with caution)
 */
export const resetProcessingState = () => {
  isProcessing = false;
};

// Alternative: If you prefer a more functional approach with closures
// export const createRazorpayService = () => {
//   let processingState = false;

//   return {
//     async createOrder(params) {
//       const { tournamentId, ageCategory, genderCategory } = params;

//       try {
//         const response = await Client.post("/payment/create-order", {
//           tournamentId,
//           action: "tournament_registration",
//           ageCategory,
//           genderCategory,
//           registrationType: "player",
//           registerAfterPayment: true,
//         });

//         if (!response.data?.success) {
//           throw new Error(response.data?.message || "Order creation failed");
//         }

//         return response.data.data;
//       } catch (error) {
//         console.error("Order creation error:", error);
//         throw error;
//       }
//     },

//     async verifyPayment(razorpayResponse) {
//       try {
//         const response = await Client.post("/payment/verify-payment", {
//           razorpay_payment_id: razorpayResponse.razorpay_payment_id,
//           razorpay_order_id: razorpayResponse.razorpay_order_id,
//           razorpay_signature: razorpayResponse.razorpay_signature,
//         });

//         if (!response.data?.success) {
//           throw new Error(
//             response.data?.message || "Payment verification failed"
//           );
//         }

//         return response.data.data;
//       } catch (error) {
//         console.error("Payment verification error:", error);
//         throw error;
//       }
//     },

//     async initiatePayment(params, toast) {
//       const {
//         tournamentId,
//         ageCategory,
//         genderCategory,
//         onStart,
//         onSuccess,
//         onError,
//         onFinally,
//       } = params;

//       if (processingState) {
//         toast.warn("Payment is already in progress");
//         return;
//       }

//       processingState = true;
//       onStart?.();

//       try {
//         const orderData = await this.createOrder({
//           tournamentId,
//           ageCategory,
//           genderCategory,
//         });

//         const options = {
//           key: orderData.razorpay_key_id,
//           amount: orderData.amount,
//           currency: orderData.currency || "INR",
//           name: orderData.name || "Tournament Registration",
//           description: orderData.description,
//           order_id: orderData.order_id,
//           prefill: {
//             name: orderData.prefill?.name,
//             email: orderData.prefill?.email,
//             contact: orderData.prefill?.contact,
//           },
//           theme: {
//             color: "#3399cc",
//           },
//           handler: async (response) => {
//             try {
//               const verificationResult = await this.verifyPayment(response);
//               onSuccess?.(verificationResult);
//               toast.success("Payment successful!");
//             } catch (error) {
//               onError?.(error);
//               toast.error("Payment verification failed");
//             }
//           },
//           modal: {
//             ondismiss: () => {
//               console.log("Payment modal closed");
//               processingState = false;
//               onFinally?.();
//             },
//           },
//         };

//         const rzp = new window.Razorpay(options);
//         rzp.open();
//       } catch (error) {
//         const errorMessage = this.getErrorMessage(error);
//         toast.error(errorMessage);
//         onError?.(error);
//         console.error("Payment initiation error:", error);
//       } finally {
//         processingState = false;
//         onFinally?.();
//       }
//     },

//     getErrorMessage(error) {
//       if (error.response?.data?.error) {
//         return error.response.data.error;
//       }
//       if (error.response?.data?.message) {
//         return error.response.data.message;
//       }
//       if (error.message) {
//         return error.message;
//       }
//       return "An error occurred while processing payment";
//     },

//     getIsProcessing: () => processingState,
//     resetProcessingState: () => { processingState = false; }
//   };
// };
