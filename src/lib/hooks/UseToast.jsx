import { useContext } from "react";
import { ToastContext } from "../../context/ToastContext";

// Custom hook to use the toast with better error handling
export const UseToast = () => {
  const context = useContext(ToastContext);
  
  if (context === undefined) {
    console.error("useToast must be used within a ToastProvider");
    // Return dummy functions to prevent crashes when used outside provider
    return {
      showToast: () => {},
      hideToast: () => {},
      success: () => {},
      error: () => {},
      info: () => {},
      warning: () => {},
      disclaimer: () => {},
    };
  }
  
  return context;
};

export default UseToast;