import { useState, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { initiateBulkPayment as razorInitiateBulkPayment } from '../services/razorpayBulkService';
import { useRazorpay } from './useRazorpay';

export const useRazorpayBulkPayment = (toast) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const { isLoaded, error: sdkError } = useRazorpay();
  const navigate = useNavigate();
  
  // Prevent multiple simultaneous bulk payments using ref
  const isProcessingRef = useRef(false);

  const initiateBulkPayment = useCallback(async (bulkRegistrationId) => {
    // Early validation checks
    if (!isLoaded) {
      const loadError = new Error('Payment system not ready');
      setError(loadError);
      return { success: false, error: loadError };
    }

    if (isProcessingRef.current) {
      const processingError = new Error('Bulk payment already in progress');
      setError(processingError);
      return { success: false, error: processingError };
    }

    // Input validation
    if (!bulkRegistrationId) {
      const validationError = new Error('Missing bulk registration ID');
      setError(validationError);
      return { success: false, error: validationError };
    }

    setError(null);
    isProcessingRef.current = true;

    try {
      const result = await razorInitiateBulkPayment({
        bulkRegistrationId,
        onStart: () => setIsLoading(true),
        onSuccess: (result) => {
          // URL encoding for safety and use replace to prevent back button issues
          const params = new URLSearchParams({
            txnid: result.transactionId,
            amount: result.amount,
            tournament: result.tournamentTitle,
            playerCount: result.playerCount || result.registrationCount,
            type: 'bulk'
          });
          navigate(`/payment-success?${params.toString()}`, { replace: true });
        },
        onError: (err) => {
          setError(err);
          // Encode error message to prevent URL injection
          const errorMsg = encodeURIComponent(err.message || 'Bulk payment failed');
          navigate(`/payment-failure?error=${errorMsg}`, { replace: true });
        },
        onFinally: () => {
          setIsLoading(false);
          isProcessingRef.current = false;
        },
      }, toast);

      return { success: true, data: result };
    } catch (err) {
      setError(err);
      isProcessingRef.current = false;
      setIsLoading(false);
      return { success: false, error: err };
    }
  }, [isLoaded, navigate]);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  // Additional utility to reset all state
  const resetState = useCallback(() => {
    setError(null);
    setIsLoading(false);
    isProcessingRef.current = false;
  }, []);

  return {
    initiateBulkPayment,
    isLoading,
    error: error || sdkError,
    resetError,
    resetState,
    isReady: isLoaded && !sdkError,
    isProcessing: isProcessingRef.current,
  };
};