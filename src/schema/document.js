import { z } from "zod";

// Schema for document upload validation
export const documentSchema = z.object({
  name: z.string().min(1, "Document name is required"),
  document: z
    .any()
    .refine(
      (file) => {
        if (!file || file === "") return false;
        return file instanceof File;
      },
      { message: "Please upload a valid file" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return false;
        return file.size <= 5 * 1024 * 1024; // 5MB limit
      },
      { message: "File must be less than 5MB" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return false;
        return [
          "image/jpeg",
          "image/png",
          "image/jpg",
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ].includes(file.type);
      },
      { message: "File must be PNG, JPG, PDF, or DOC format" }
    ),
});

// Schema for document list item
export const documentItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  fileType: z.string(),
  size: z.number(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

// Schema for document list response
export const documentListSchema = z.array(documentItemSchema);
