import { z } from "zod";

// Base Schema
const baseProfileSchema = z.object({
  playerTitle: z.string().optional(),

  dob: z
    .string({ errorMap: () => ({ message: "Date of birth is required" }) })
    .refine((date) => {
      const currentDate = new Date();
      const dobDate = new Date(date);
      const age = currentDate.getFullYear() - dobDate.getFullYear();
      return age >= 5 && age <= 150;
    }, "Age should be above 5"),

  alternateContact: z.string().optional(),
  gender: z.enum(["male", "female", "other"]),
  fideRating: z.string().optional(),
  aicfId: z.string().optional(),
  districtId: z.string().optional(),
  club: z.string().optional(),

  state: z.string().min(1, "State is required"),
  city: z.string().min(1, "City is required"),
  pincode: z.string().regex(/^\d{6}$/, "Invalid pincode"),
  address: z.string().min(10, "Address must be at least 10 characters"),
  parentGuardianName: z.string().optional(),
  emergencyContact: z.string().optional(),
  fideId: z.string().optional(),
  association: z.string().optional(),
  district: z.string().min(1, "District is required"),
  country: z.string().min(1, "Country is required"),
  profileImage: z
    .any()
    .optional()
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file instanceof File;
      },
      { message: "Please upload a valid file" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file.size <= 1 * 1024 * 1024; // 5MB limit
      },
      { message: "File must be less than 5MB" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return ["image/jpeg", "image/png", "image/jpg", "image/webp"].includes(
          file.type
        );
      },
      { message: "File must be JPEG, PNG, or WebP format" }
    ),
  // This field is for preview purposes only, not sent to API
  profileImagePreview: z.string().optional(),

  termsAndConditions: z
    .boolean({ errorMap: () => ({ message: "Required" }) })
    .refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    }),

  // Additional fields that are only relevant in edit mode
  phoneNumber: z
    .string()
    .min(10, { message: "Mobile number is required" })
    .regex(/^(91)?\d{10}$/, {
      message: "Phone number be 10 digits",
    })
    .max(12, {
      message: "Mobile number must be 12 digits (91 + 10 digits)",
    }),
  firstName: z.string().min(2, "First Name must be at least 2 characters"),
  lastName: z.string().min(2, "Last Name must be at least 2 characters"),
  otp: z.string().length(6),
  myfile: z
    .any()
    .optional()
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file instanceof File;
      },
      { message: "Please upload a valid file" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file.size <= 10 * 1024 * 1024; // 10MB limit
      },
      { message: "File must be less than 10MB" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return [
          "image/jpeg",
          "image/png",
          "image/jpg",
          "image/webp",
          "application/pdf",
        ].includes(file.type);
      },
      { message: "File must be PDF, or JPEG, PNG, or WebP format" }
    ),
  myfilePreview: z.string().optional(),


});

// 🔒 Full schema for profile creation (no OTP, phone, etc.)
export const playerProfileSchema = baseProfileSchema
  .omit({
    otp: true,
    phoneNumber: true,
    firstName: true,
    lastName: true,
  })
  .superRefine((data, ctx) => {
    if (data.dob) {
      const currentDate = new Date();
      const dobDate = new Date(data.dob);
      let age = currentDate.getFullYear() - dobDate.getFullYear();
      const monthDiff = currentDate.getMonth() - dobDate.getMonth();

      if (
        monthDiff < 0 ||
        (monthDiff === 0 && currentDate.getDate() < dobDate.getDate())
      ) {
        age--;
      }

      if (age < 18) {
        if (!data.parentGuardianName || data.parentGuardianName.trim() === "") {
          ctx.addIssue({
            path: ["parentGuardianName"],
            message:
              "Parent/Guardian Name is required for players under 18 years old",
            code: z.ZodIssueCode.custom,
          });
        }
        if (!data.emergencyContact || data.emergencyContact.trim() === "") {
          ctx.addIssue({
            path: ["emergencyContact"],
            message:
              "Emergency Contact is required for players under 18 years old",
            code: z.ZodIssueCode.custom,
          });
        }
      }
    }
  });

export const arbiterDetailSchema = z.object({
  title: z.string().optional(),
  officialId: z.string().optional(),
  alternateContact: z.string().optional(),
  fideRating: z.string().optional(),
  aicfId: z.string().optional(),
  districtId: z.string().optional(),

  state: z.string().min(1, "State is required"),
  city: z.string().min(1, "City is required"),
  pincode: z.string().regex(/^\d{6}$/, " pincode must be 6 digits"),
  profileImage: z
    .any()
    .optional()
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file instanceof File;
      },
      { message: "Please upload a valid file" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return file.size <= 1 * 1024 * 1024; // 5MB limit
      },
      { message: "File must be less than 5MB" }
    )
    .refine(
      (file) => {
        if (!file || file === "") return true; // Skip validation if no file
        return ["image/jpeg", "image/png", "image/jpg", "image/webp"].includes(
          file.type
        );
      },
      { message: "File must be JPEG, PNG, or WebP format" }
    ),
  // This field is for preview purposes only, not sent to API
  profileImagePreview: z.string().optional(),

  parentGuardianName: z.string().optional(),
  emergencyContact: z.string().optional(),
  fideId: z.string().optional(),

  district: z.string().min(1, "District is required"),
  country: z.string().min(1, "Country is required"),

  // Additional fields that are only relevant in edit mode
  phoneNumber: z
    .string()
    .min(10, { message: "Mobile number is required" })
    .regex(/^(91)?\d{10}$/, {
      message: "Phone number be 10 digits",
    })
    .max(12, {
      message: "Mobile number must be 10 digits",
    }),
  firstName: z.string().min(2, "First Name must be at least 2 characters"),
  lastName: z.string().min(2, "Last Name must be at least 2 characters"),
});

// 📝 Editable profile (includes optional OTP, phone, name)
export const playerProfileEditSchema = baseProfileSchema.partial();
