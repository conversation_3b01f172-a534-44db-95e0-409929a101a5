/**
 * Formats a number by removing decimal places if they are all zeros
 * For example, converts "3000.00" to "3000" but keeps "3000.50" as is
 *
 * @param {string|number} value - The number to format
 * @returns {string} - The formatted number as a string
 */
export const formatDecimal = (value) => {
  if (value === null || value === undefined || value === "") {
    return "";
  }

  // Convert to string if it's not already
  const stringValue = String(value);

  // Check if the value has a decimal point
  if (stringValue.includes(".")) {
    // Split the number into integer and decimal parts
    const [integerPart, decimalPart] = stringValue.split(".");

    // If decimal part is all zeros or empty, return just the integer part
    if (!decimalPart || /^0+$/.test(decimalPart)) {
      return integerPart;
    }

    // Otherwise return the original value (with decimal)
    return stringValue;
  }

  // If there's no decimal point, return as is
  return stringValue;
};

export function formatDateToDMY(dateStr) {
  const [year, month, day] = dateStr.split("-");
  return `${day}-${month}-${year}`;
}

export function capitalizeFirstLetter(string) {
  if (typeof string !== 'string' || string.length === 0) {
    return '';
  }
  return string.charAt(0).toUpperCase() + string.slice(1);
}


export function formatAgeCategory(ageCategory) {
  if (!ageCategory) return "-";
  if (Array.isArray(ageCategory)) {
    return ageCategory.map(capitalizeForAge).join(", ");
  }
  return capitalizeForAge(ageCategory);
}

export function capitalizeForAge(string) {
  if (typeof string !== 'string' || string.length === 0) {
    return '';
  }
  return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
}

export function capitalizeWords(str) {
  if (typeof str !== "string") return "";
  return str.replace(/\b\w/g, (char) => char.toUpperCase());
}

/**
 * Formats a 10-digit phone number into '##### #####' format
 * @param {string | number} phoneNumber - The phone number to format
 * @returns {string} - Formatted phone number
 */
export function formatPhoneNumber(phoneNumber) {
  const digits = phoneNumber.toString().replace(/\D/g, '');

  if (digits.length !== 10) {
    console.warn('Phone number is not 10 digits:', phoneNumber);
    return phoneNumber;
  }

  return `${digits.slice(0, 5)} ${digits.slice(5)}`;
}