export function isTournamentOngoing(startDateStr, endDateStr, reportingTimeStr) {
  if (!startDateStr || !endDateStr || !reportingTimeStr) return false;

  const now = new Date();

  const tournamentStartDate = new Date(startDateStr);
  const tournamentEndDate = new Date(endDateStr);

  if (isNaN(tournamentStartDate) || isNaN(tournamentEndDate)) return false;

  const endDateISO = tournamentEndDate.toISOString().split("T")[0]; // safe now
  const fullReportingDateTimeStr = `${endDateISO} ${reportingTimeStr}`;

  const tournamentEndDateTime = new Date(fullReportingDateTimeStr);
  if (isNaN(tournamentEndDateTime)) return false;

  return now >= tournamentStartDate && now <= tournamentEndDateTime;
}

export function isWithinRegistrationPeriod(
  startDateStr,
  endDateStr,
  endTimeStr
) {
  const now = new Date();

  const registrationStartDate = new Date(startDateStr);
  const registrationEndDate = new Date(endDateStr);

  // Combine end date and time into a single datetime string
  const endDateISO = registrationEndDate.toISOString().split("T")[0]; // 'YYYY-MM-DD'
  const fullEndDateTimeStr = `${endDateISO} ${endTimeStr}`; // e.g., '2025-07-02 11:59 PM'

  const registrationEndDateTime = new Date(fullEndDateTimeStr);

  // Return true if current time is between start and end
  return now >= registrationStartDate && now <= registrationEndDateTime;
}

export function isTournamentConducted(endDateStr) {
  const now = new Date();
  const today = new Date(now.toDateString()); // Strip time for accurate comparison
  const tournamentEndDate = new Date(endDateStr);

  return today > tournamentEndDate;
}
export function hasTournamentStarted(startDateStr) {
  if (!startDateStr) return false;
  const now = new Date();
  const startDate = new Date(startDateStr);
  return now >= startDate;
}
