// Simple encryption utilities with variable reversal and btoa
const secretKey = "myApp2024Auth";

// XOR function
const xorEncrypt = (text, key) => {
  let result = "";
  for (let i = 0; i < text.length; i++) {
    result += String.fromCharCode(
      text.charCodeAt(i) ^ key.charCodeAt(i % key.length)
    );
  }
  return result;
};

// Reverse string
const reverseString = (str) => {
  return str.split("").reverse().join("");
};

// Add decoy variables and obfuscate
const addNoise = (data) => {
  const fakeData = {
    config: "app_settings",
    version: "1.0.0",
    theme: "dark",
    user_data: data, // Real data here
    cache: "enabled",
    debug: false,
    timestamp: Date.now(),
  };
  return fakeData;
};

// Extract real data
const removeNoise = (noisyData) => {
  return noisyData.user_data;
};

// Main encryption function
const encrypt = (data) => {
  try {
    // Step 1: Convert to JSON
    let jsonData = JSON.stringify(data);

    // Step 2: Add noise/decoy data
    const noisyData = addNoise(data);
    jsonData = JSON.stringify(noisyData);

    // Step 3: XOR encrypt
    let encrypted = xorEncrypt(jsonData, secretKey);

    // Step 4: Reverse the string
    encrypted = reverseString(encrypted);

    // Step 5: Base64 encode
    encrypted = btoa(encrypted);

    // Step 6: Reverse again
    encrypted = reverseString(encrypted);

    // Step 7: Final base64
    return btoa(encrypted);
  } catch (error) {
    console.error("Encryption failed:", error);
    return null;
  }
};

// Main decryption function
const decrypt = (encryptedData) => {
  try {
    if (!encryptedData) return null;

    // Step 1: Reverse final base64
    let decrypted = atob(encryptedData);

    // Step 2: Reverse string
    decrypted = reverseString(decrypted);

    // Step 3: Base64 decode
    decrypted = atob(decrypted);

    // Step 4: Reverse string again
    decrypted = reverseString(decrypted);

    // Step 5: XOR decrypt
    decrypted = xorEncrypt(decrypted, secretKey);

    // Step 6: Parse JSON
    const parsedData = JSON.parse(decrypted);

    // Step 7: Remove noise and get real data
    return removeNoise(parsedData);
  } catch (error) {
    console.error("Decryption failed:", error);
    return null;
  }
};

// Export functions
export { encrypt, decrypt };
