import React, { useState } from "react";
import { Box, Typography, Container, Paper, Snackbar, Alert } from "@mui/material";
import { DetailTable } from "../components/common/DetailTable";
import { Edit, Delete, ContentCopy, Download } from "@mui/icons-material";

const DetailTableWithButtonsExample = () => {
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: "",
    severity: "success",
  });

  // Example functions for button actions
  const handleEdit = (field) => {
    setSnackbar({
      open: true,
      message: `Editing ${field}...`,
      severity: "info",
    });
  };

  const handleCopy = (value) => {
    navigator.clipboard.writeText(value);
    setSnackbar({
      open: true,
      message: "Copied to clipboard!",
      severity: "success",
    });
  };

  const handleDelete = (field) => {
    setSnackbar({
      open: true,
      message: `Deleting ${field}...`,
      severity: "warning",
    });
  };

  const handleDownload = (field) => {
    setSnackbar({
      open: true,
      message: `Downloading ${field}...`,
      severity: "info",
    });
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Example data with buttons
  const playerDetails = [
    {
      label: "Player Name",
      value: "Magnus Carlsen",
    },
    {
      label: "FIDE ID",
      value: "1503014",
      hasButton: true,
      buttonText: "Copy",
      buttonIcon: <ContentCopy />,
      buttonColor: "primary",
      buttonVariant: "outlined",
      onButtonClick: () => handleCopy("1503014"),
    },
    {
      label: "Rating",
      value: "2853",
    },
    {
      label: "Country",
      value: "Norway",
    },
    {
      label: "Birth Date",
      value: "November 30, 1990",
    },
    {
      label: "Title",
      value: "Grandmaster",
    },
    {
      label: "Email",
      value: "<EMAIL>",
      isLink: true,
      url: "mailto:<EMAIL>",
    },
    {
      label: "Documents",
      value: "Player Certificate",
      buttons: [
        {
          text: "Download",
          icon: <Download />,
          color: "primary",
          variant: "outlined",
          onClick: () => handleDownload("Player Certificate"),
        },
        {
          text: "Delete",
          icon: <Delete />,
          color: "error",
          variant: "outlined",
          onClick: () => handleDelete("Player Certificate"),
        },
      ],
    },
    {
      label: "Profile",
      value: "Player profile information can be edited",
      hasButton: true,
      buttonText: "Edit Profile",
      buttonIcon: <Edit />,
      buttonColor: "secondary",
      onButtonClick: () => handleEdit("profile"),
    },
  ];

  // Custom row colors
  const customRowColor = {
    odd: "#f5f5f5",
    even: "#ffffff",
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
          Player Details
        </Typography>

        <DetailTable details={playerDetails} rowColor={customRowColor} />
      </Paper>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: "100%" }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default DetailTableWithButtonsExample;
