import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Box,
  Container,
  Paper,
  Tabs,
  Tab,
  Typography,
  Badge,
  Divider,
  Skeleton,
  useTheme,
  useMediaQuery,
  IconButton,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import {
  Notifications as NotificationsIcon,
  People as PeopleIcon,
  Business as BusinessIcon,
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import JoinRequestsTab from "../../components/club/JoinRequestsTab";

import GeneralNotificationsTab from "../../components/notifications/GeneralNotificationsTab";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import BackButton from "../../components/common/BackButton";

// Tab panel component to handle tab content
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`notifications-tabpanel-${index}`}
      aria-labelledby={`notifications-tab-${index}`}
      {...other}
      style={{ width: "100%" }}
    >
      {value === index && <Box sx={{ p: { xs: 2, md: 3 } }}>{children}</Box>}
    </div>
  );
}

const ClubNotificationsPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [counts, setCounts] = useState({
    general: 0,
    friendRequests: 0,
    clubInvites: 0,
  });
  const [loading, setLoading] = useState(true);

  const location = useLocation();
  const navigate = useNavigate();
  const toast = UseToast();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    // Update URL based on selected tab
    const paths = [
      "/dashboard/notifications",

      "/dashboard/notifications/club-jion-requests",
    ];
    navigate(paths[newValue]);
  };

  //   // Fetch notification counts
  //   const fetchNotificationCounts = async (isRefresh = false) => {
  //     if (!isRefresh) {
  //       setLoading(true);
  //     }

  //     try {
  //       const response = await Client.get("/notifications/counts");
  //       if (response.data.success) {
  //         setCounts({
  //           general: response.data.data.general || 0,
  //           friendRequests: response.data.data.friendRequests || 0,
  //           clubInvites: response.data.data.clubInvites || 0,
  //         });
  //         if (isRefresh) {
  //           toast.success("Notifications refreshed");
  //         }
  //       }
  //     } catch (error) {
  //       console.error("Error fetching notification counts:", error);
  //       toast.error("Failed to fetch notification counts");
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  // Set active tab based on URL
  useEffect(() => {
    const path = location.pathname;
    if (path.includes("friend-requests")) {
      setActiveTab(1);
    } else if (path.includes("club-invites")) {
      setActiveTab(2);
    } else {
      setActiveTab(0);
    }
  }, [location.pathname]);

  //   // Initial data fetch
  //   useEffect(() => {
  //     fetchNotificationCounts();
  //   }, []);

  // Calculate total notifications
  // const totalNotifications =
  //   counts.general + counts.friendRequests + counts.clubInvites;

  return (
    <Container maxWidth="xl" sx={{ py: { xs: 2, md: 4 } }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          mb: 3,
          gap: 2,
        }}
      >
        <BackButton />
        {/* <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Tooltip title="Go back">
            <IconButton
              onClick={handleGoBack}
              sx={{
                bgcolor: theme.palette.background.paper,
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                "&:hover": {
                  bgcolor: theme.palette.background.paper,
                  boxShadow: "0 4px 8px rgba(0,0,0,0.15)",
                },
              }}
            >
              <ArrowBackIcon />
            </IconButton>
          </Tooltip>

          <Typography variant="h5" component="h1" fontWeight="600">
            Notifications
            {!loading && totalNotifications > 0 && (
              <Badge
                badgeContent={totalNotifications}
                color="error"
                max={99}
                sx={{
                  ml: 2,
                  "& .MuiBadge-badge": {
                    fontSize: 14,
                    height: 22,
                    minWidth: 22,
                  },
                }}
              />
            )}
          </Typography>
        </Box> */}
      </Box>

      <Paper
        elevation={2}
        sx={{
          borderRadius: 3,
          overflow: "hidden",
          transition: "all 0.3s ease",
          bgcolor: theme.palette.background.paper,
          "&:hover": {
            boxShadow: theme.shadows[4],
          },
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            height: "100%",
          }}
        >
          {/* Tabs */}
          <Box
            sx={{
              bgcolor: theme.palette.mode === "light" ? "grey.50" : "grey.900",
              width: { sm: "250px", md: "100%" },
            }}
          >
            <Tabs
              orientation={"horizontal"}
              variant="scrollable"
              value={activeTab}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              sx={{
                "& .MuiTab-root": {
                  alignItems: isMobile ? "center" : "flex-start",
                  justifyContent: isMobile ? "center" : "flex-start",
                  textAlign: "left",
                  py: 2.5,
                  px: { xs: 2, md: 3 },
                  minHeight: 64,
                  transition: "all 0.2s ease",
                  "&.Mui-selected": {
                    bgcolor:
                      theme.palette.mode === "light"
                        ? "rgba(25, 118, 210, 0.08)"
                        : "rgba(144, 202, 249, 0.08)",
                  },
                  "&:hover": {
                    bgcolor:
                      theme.palette.mode === "light"
                        ? "rgba(0, 0, 0, 0.04)"
                        : "rgba(255, 255, 255, 0.04)",
                  },
                },
              }}
            >
              {/* <Tab
                icon={
                  loading ? (
                    <Skeleton variant="circular" width={24} height={24} />
                  ) : (
                    <Badge badgeContent={counts.general} color="error" max={99}>
                      <NotificationsIcon sx={{ color: "text.primary" }} />
                    </Badge>
                  )
                }
                iconPosition="start"
                label={
                  <Typography sx={{ ml: { md: 1 }, color: "text.primary" }}>
                    Notifications
                  </Typography>
                }
                sx={{ textTransform: "none" }}
              /> */}
              {/* <Tab
                icon={
                  loading ? (
                    <Skeleton variant="circular" width={24} height={24} />
                  ) : (
                    <Badge
                      badgeContent={counts.friendRequests}
                      color="error"
                      max={99}
                    >
                      <PeopleIcon sx={{ color: "text.primary" }} />
                    </Badge>
                  )
                }
                iconPosition="start"
                label={
                  <Typography sx={{ ml: { md: 1 }, color: "text.primary" }}>
                    Friend Requests
                  </Typography>
                }
                sx={{ textTransform: "none" }}
              /> */}
              <Tab
                icon={
                  loading ? (
                    <Skeleton variant="circular" width={24} height={24} />
                  ) : (
                    <Badge
                      badgeContent={counts.clubInvites}
                      color="error"
                      max={99}
                    >
                      <BusinessIcon sx={{ color: "text.primary" }} />
                    </Badge>
                  )
                }
                iconPosition="start"
                label={
                  <Typography sx={{ ml: { md: 1 }, color: "text.primary" }}>
                    Join Requests
                  </Typography>
                }
                sx={{ textTransform: "none" }}
              />
            </Tabs>
          </Box>

          {/* Tab Content */}
          <Box sx={{ flexGrow: 1, height: "100%" }}>
            {/* <TabPanel value={activeTab} index={0}>
              <GeneralNotificationsTab loading={loading} />
            </TabPanel>
            <TabPanel value={activeTab} index={1}>
              <FriendRequestsTab loading={loading} />
            </TabPanel> */}
            <TabPanel value={activeTab} index={0}>
              <JoinRequestsTab loading={false} />
            </TabPanel>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default ClubNotificationsPage;
