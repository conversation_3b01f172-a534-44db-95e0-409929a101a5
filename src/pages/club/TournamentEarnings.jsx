import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Card,
  CardContent,
  Autocomplete,
  CircularProgress,
  MenuItem,
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { Link } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import PaymentIcon from "@mui/icons-material/Payment";
import PersonIcon from "@mui/icons-material/Person";
import EventIcon from "@mui/icons-material/Event";
import { RestartAlt } from "@mui/icons-material";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const TournamentEarningsPage = () => {
  const [clubPayments, setClubPayments] = useState([]);
  const [playerPayments, setPlayerPayments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [tournamentLoading, setTournamentLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);

  const [shouldFetch, setShouldFetch] = useState(false);
  const [tournaments, setTournaments] = useState([]);
  const [selectedTournament, setSelectedTournament] = useState(null);
  const [summaryStats, setSummaryStats] = useState({
    totalPayers: 0,
    totalAmount: 0,
    currency: "INR",
    successfulPayments: 0,
    pendingPayments: 0,
    failedPayments: 0,
  });
  // States for form inputs
  const [search, setSearch] = useState({
    transactionId: "",
    tournamentTitle: "",
    paymentType: "all",
  });
  const [page, setPage] = useState(1);
  const limit = 10; // Adjust as needed
  const toast = UseToast();

  const handlePageChange = (_, value) => {
    setPage(value);
    fetchPayments(value);
  };

  // Handle tournament selection
  const handleTournamentChange = (_, newValue) => {
    setSelectedTournament(newValue); // Store the whole tournament object
    if (newValue && newValue.title) {
      // Use the tournament title for search
      setSearch((prev) => ({ ...prev, tournamentTitle: newValue.title }));
    } else {
      setSearch((prev) => ({ ...prev, tournamentTitle: "" }));
    }
  };

  const handleReset = () => {
    setSearch({
      transactionId: "",
      tournamentTitle: "",
      paymentType: "all",
    });
    setSelectedTournament(null);
    setShouldFetch(true);
  };

  const handleSearch = () => {
    setShouldFetch(true);
  };

  // Define fetchPayments before using it in useEffect
  const fetchPayments = useCallback(
    async (pageNumber) => {
      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
        };
        if (search.tournamentTitle !== "") {
          params.tournamentTitle = search.tournamentTitle;
        }
        if (search.transactionId !== "") {
          params.transactionId = search.transactionId;
        }
        if (search.paymentType !== "all") {
          params.paymentType = search.paymentType;
        }

        const response = await Client.get("/payment/club/tournament", {
          params,
        });

        if (response.status === 204) {
          toast.info("No payment records found");
          setClubPayments([]);
          setPlayerPayments([]);
          setTotalPages(0);
          setPage(1);
          setSummaryStats({
            totalPayers: 0,
            totalAmount: 0,
            currency: "INR",
            successfulPayments: 0,
            pendingPayments: 0,
            failedPayments: 0,
          });
          return;
        }

        if (!response.data.success) {
          toast.info("No payment records found");
          return;
        }

        const paymentsData = response.data.data.payments;

        // Split payments by type
        const clubPaymentsData = paymentsData.filter(
          (payment) => payment.paymentType === "club"
        );
        const playerPaymentsData = paymentsData.filter(
          (payment) => payment.paymentType === "player"
        );

        setClubPayments(clubPaymentsData);
        setPlayerPayments(playerPaymentsData);

        const totalPage = Math.ceil(response.data.data.total / limit);
        setTotalPages(totalPage);

        // Calculate and set summary statistics
        const stats = {
          totalPayers: 0,
          totalAmount: 0,
          currency: "INR",
          successfulPayments: 0,
          pendingPayments: 0,
          failedPayments: 0,
        };
        stats.totalPayers = paymentsData.reduce((acc, payment) => {
          if (
            search.paymentType === "all" ||
            payment.paymentType === search.paymentType
          ) {
            if (payment.paymentType === "club") {
              acc += payment.playersCount || 0;
            } else if (payment.paymentType === "player") {
              acc += 1;
            }
          }
          return acc;
        }, 0);

        paymentsData.forEach((payment) => {
          // Add to total amount
          stats.totalAmount += parseFloat(payment.paymentAmount || 0);

          // Count by status
          const status = payment.paymentStatus?.toLowerCase();
          if (status === "paid" || status === "success") {
            stats.successfulPayments++;
          } else if (status === "pending") {
            stats.pendingPayments++;
          } else if (status === "failed") {
            stats.failedPayments++;
          }

          // Set currency if available
          if (payment.paymentCurrency) {
            stats.currency = payment.paymentCurrency;
          }
        });

        setSummaryStats(stats);
      } catch (error) {
        if (error instanceof AxiosError && error.response) {
          toast.info(error.response.data.error);
          return;
        }
        console.error("Error fetching payment history:", error);
        toast.info("An error occurred. Please try again later.");
      } finally {
        setLoading(false);
      }
    },
    [limit, search]
  );

  // Define fetchTournaments with useCallback
  const fetchTournaments = useCallback(async () => {
    setTournamentLoading(true);
    try {
      const response = await Client.get("/tournament/club", {
        params: { limit: 100, page: 1 }, // Get all tournaments
      });

      if (response.status === 204 || !response.data.success) {
        setTournaments([]);
        return;
      }

      setTournaments(response.data.data.tournaments || []);
    } catch (error) {
      console.error("Error fetching tournaments:", error);
      toast.error("Failed to load tournaments");
    } finally {
      setTournamentLoading(false);
    }
  }, []);

  useEffect(() => {
    if (shouldFetch) {
      fetchPayments(1);
      setShouldFetch(false);
    }
  }, [shouldFetch, fetchPayments]);

  // Fetch tournaments on component mount
  useEffect(() => {
    fetchTournaments();
  }, []);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return date.toLocaleString("en-US", options);
  };

  const fetchPaymentReport = async () => {
    // return;
    // setLoading(true);

    try {
      const response = await Client.get("/report/payment", {
        params: { ...search },
        responseType: "blob",
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "Players_report.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

    console.log("clubPayments",clubPayments)

  return (
    <Container
      maxWidth="xl"
      sx={{
        py: 4,
        pt: 2,
        pb: 8,
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <BackButton />
      {(clubPayments.length > 0 || playerPayments.length>0) && 
      <Box mb={2} sx={{display:'flex',justifyContent:'flex-end',alignItems:'center'}}>
        <Button size="small" variant="contained" disabled={tournaments?.length === 0} onClick={fetchPaymentReport}>Download report</Button>
      </Box>}
      {/* Search Form */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f9f9f9", borderRadius: 2, boxShadow: 3 }}
      >
        <Typography
          variant="h5"
          gutterBottom
          sx={{ mb: 2, fontWeight: "bold", color: "#3f51b5" }}
        >
          Tournament Payment History
        </Typography>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={6} md={6}>
            <Autocomplete
              options={tournaments}
              getOptionLabel={(option) =>
                option && option.title ? option.title.replace(/-/g, " ") : ""
              }
              value={selectedTournament}
              onChange={handleTournamentChange}
              loading={tournamentLoading}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  placeholder="Select a tournament"
                  fullWidth
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <React.Fragment>
                        {tournamentLoading ? (
                          <CircularProgress color="inherit" size={20} />
                        ) : null}
                        {params.InputProps.endAdornment}
                      </React.Fragment>
                    ),
                  }}
                  sx={{ bgcolor: "white" }}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <TextField
              variant="outlined"
              fullWidth
              value={search.transactionId}
              onChange={(e) =>
                setSearch({ ...search, transactionId: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Enter transaction ID"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              select
              variant="outlined"
              fullWidth
              value={search.paymentType || "all"}
              onChange={(e) =>
                setSearch({ ...search, paymentType: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Select payment type"
            >
              <MenuItem value="all">All</MenuItem>
              <MenuItem value="club">Club</MenuItem>
              <MenuItem value="player">Player</MenuItem>
            </TextField>
          </Grid>

          <Grid item xs={12} sm={6} md={12} sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="outlined"
              color="secondary"
              sx={{
                width: "40px",
              }}
              onClick={handleReset}
              disabled={loading}
            >
              <RestartAlt />
            </Button>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleSearch}
              startIcon={<SearchIcon />}
              sx={{
                bgcolor: "#3f51b5",
                textTransform: "none",
                height: "56px",
                fontSize: "16px",
                fontWeight: "bold",
              }}
            >
              Search
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Summary Statistics */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f5f9ff", borderRadius: 2, boxShadow: 2 }}
      >
        <Typography
          variant="h6"
          gutterBottom
          sx={{ fontWeight: "bold", color: "#3f51b5" }}
        >
          Payment Summary
        </Typography>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e3f2fd", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary">
                  Total Payers
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.totalPayers}
                </Typography>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  <PersonIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Players
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="secondary">
                  Total Amount
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.totalAmount.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" color="secondary" sx={{ mt: 1 }}>
                  <PaymentIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Collected
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#f3e5f5", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary">
                  Tournament
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textTransform: "capitalize",
                    textAlign: "center",
                  }}
                >
                  {selectedTournament && selectedTournament.title
                    ? selectedTournament.title.replace(/-/g, " ")
                    : "All Tournaments"}
                </Typography>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  <EventIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  {selectedTournament && selectedTournament.startDate
                    ? new Date(selectedTournament.startDate).toLocaleDateString(
                        "en-US",
                        { day: "2-digit", month: "short", year: "numeric" }
                      )
                    : "All Dates"}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      {/* Club Payment History Table */}
      {(search.paymentType === "all" || search.paymentType === "club") && (
        <>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ mt: 4, mb: 2, fontWeight: "bold", color: "#3f51b5" }}
          >
            Club Payments
          </Typography>
          <DynamicTable
            columns={[
              {
                id: "clubName",
                label: "ClubName",
                format: (_, payment) => (
                  <Typography
                    component={Link}
                    to={`/clubs/${payment.clubId}`}
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.clubName || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "transactionId",
                label: "Transaction ID",
                format: (_, payment) => payment.paymentTransactionId || "N/A",
              },
              {
                id: "date",
                label: "Date",
                format: (_, payment) => formatDate(payment.paymentDate),
              },
              {
                id: "amount",
                label: "Amount",
                format: (_, payment) => (
                  <Typography variant="h6" fontSize={"16px"}>
                    {payment.paymentCurrency || "INR"}{" "}
                    {payment.paymentAmount || "0.00"}
                  </Typography>
                ),
              },
              {
                id: "playersCount",
                label: "Players Count",
                format: (_, payment) => (
                  <Typography
                    variant="h6"
                    fontSize={"16px"}
                    fontWeight="medium"
                  >
                    {payment.playersCount || "0"}
                  </Typography>
                ),
              },
              {
                id: "registrationType",
                label: "Registration Type",
                format: (_, payment) => payment.registrationType || "N/A",
              },
              {
                id: "paymentStatus",
                label: "Status",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      color:
                        payment.paymentStatus?.toLowerCase() === "captured"
                          ? "green"
                          : payment.paymentStatus?.toLowerCase() === "pending"
                          ? "orange"
                          : "red",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.paymentStatus === "captured" ? "Paid" : payment.paymentStatus || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "tournament",
                label: "Tournament",
                format: (_, payment) =>
                  payment?.tournament?.title || payment.tournamentTitle ? (
                    <Link
                      to={`/tournaments/${
                        payment?.tournament?.title || payment.tournamentTitle
                      }`}
                      style={{ textDecoration: "none", color: "inherit" }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          textTransform: "capitalize",
                          fontWeight: "medium",
                          textWrap: "balance",
                          fontSize: "16px",
                        }}
                      >
                        {(payment?.tournament?.title || payment.tournamentTitle)
                          .toLowerCase()
                          .replace(/-/g, " ")}
                      </Typography>
                    </Link>
                  ) : (
                    "N/A"
                  ),
              },
            ]}
            data={clubPayments}
            loading={loading}
            page={page}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            detailsPath="/players/"
            idField="id"
            showDetailsButton={false}

            tableContainerProps={{
              sx: {
                minHeight: "200px",
                maxHeight: "400px",
              },
            }}
          />
        </>
      )}

      {/* Player Payment History Table */}
      {(search.paymentType === "all" || search.paymentType === "player") && (
        <>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ mt: 4, mb: 2, fontWeight: "bold", color: "#3f51b5" }}
          >
            Player Payments
          </Typography>
          <DynamicTable
            columns={[
              {
                id: "playerName",
                label: "Player Name",
                format: (_, payment) => (
                  <Typography
                    variant="h6"
                    sx={{ fontWeight: "medium", fontSize: "16px" }}
                  >
                    {payment.playerName || "Unknown Player"}
                  </Typography>
                ),
              },
              {
                id: "cbid",
                label: "CBID",
                format: (_, payment) => (
                  <Typography
                    component={Link}
                    to={`/players/${payment.player?.cbid}`}
                    sx={{ color: "#1976d2", textDecoration: "none" }}
                  >
                    {payment.cbid || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "transactionId",
                label: "Transaction ID",
                format: (_, payment) => payment.paymentTransactionId || "N/A",
              },
              {
                id: "date",
                label: "Date",
                format: (_, payment) => formatDate(payment.paymentDate),
              },
              {
                id: "amount",
                label: "Amount",
                format: (_, payment) => (
                  <Typography variant="h6" fontSize={"16px"}>
                    {payment.paymentCurrency || "INR"}{" "}
                    {payment.paymentAmount || "0.00"}
                  </Typography>
                ),
              },
              {
                id: "paymentMethod",
                label: "Payment Method",
                format: (_, payment) => payment.paymentMode || "Online",
              },
              {
                id: "paymentStatus",
                label: "Status",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      color:
                        payment.paymentStatus?.toLowerCase() === "captured"
                          ? "green"
                          : payment.paymentStatus?.toLowerCase() === "pending"
                          ? "orange"
                          : "red",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.paymentStatus === "captured" ? "Paid" : payment.paymentStatus || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "tournament",
                label: "Tournament",
                format: (_, payment) =>
                  payment?.tournament?.title || payment.tournamentTitle ? (
                    <Link
                      to={`/tournaments/${
                        payment?.tournament?.title || payment.tournamentTitle
                      }`}
                      style={{ textDecoration: "none", color: "inherit" }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          textTransform: "capitalize",
                          fontWeight: "medium",
                          textWrap: "balance",
                          fontSize: "16px",
                        }}
                      >
                        {(payment?.tournament?.title || payment.tournamentTitle)
                          .toLowerCase()
                          .replace(/-/g, " ")}
                      </Typography>
                    </Link>
                  ) : (
                    "N/A"
                  ),
              },
            ]}
            data={playerPayments}
            loading={loading}
            page={page}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            detailsPath="/players/"
            idField="id"
            showDetailsButton={false}
       
            tableContainerProps={{
              sx: {
                minHeight: "200px",
                maxHeight: "400px",
              },
            }}
          />
        </>
      )}
    </Container>
  );
};

export default TournamentEarningsPage;
