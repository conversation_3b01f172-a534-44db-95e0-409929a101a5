import React from "react";
import ClubProfileForm from "../../components/form/ClubProfileForm";
import { Box, Container } from "@mui/material";
import BackButton from "../../components/common/BackButton";

const ClubProfileEdit = () => {
  const [edit] = React.useState(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const editParam = urlParams.get("edit");
    return editParam === "1";
  });

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton to={"/dashboard"} />
      <Box maxWidth="lg" minHeight={"100vh"} sx={{ margin: "auto", my: 2 }}>
        <ClubProfileForm edit={edit} />
      </Box>
    </Container>
  );
};

export default ClubProfileEdit;
