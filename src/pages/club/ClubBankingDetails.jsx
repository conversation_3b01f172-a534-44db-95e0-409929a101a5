import React, { useEffect, useState } from "react";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
// import { useNavigate } from "react-router-dom";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Box,
  Button,
  Grid,
  Typography,
  Paper,
  Divider,
  Alert,
  Skeleton,
} from "@mui/material";
import Spinner from "../../components/common/Spinner";
import FormTextField from "../../components/form/FormTextField";
import FormNumberField from "../../components/form/FormNumberField";
import BankDetailsUpdateRequest from "../../components/form/BankDetailsUpdateRequest";
import BackButton from "../../components/common/BackButton";

const bankDetailsSchema = z.object({
  bankName: z
    .string()
    .min(2, "Bank Name is required")
    .max(100, "Bank Name is too long"),
  AccountNumber: z
    .string()
    .min(9, "Account Number must be at least 9 digits")
    .max(18, "Account Number must not exceed 18 digits")
    .regex(/^\d+$/, "Account Number must contain only digits"),
  branchIFSCCode: z
    .string()
    .regex(/^[A-Z]{4}0[A-Z0-9]{6}$/, "Invalid IFSC code")
    .length(11, "IFSC code must be exactly 11 characters"),
  branchName: z
    .string()
    .min(2, "Branch Name is required")
    .max(100, "Branch Name is too long"),
  bankAccountType: z
    .string()
    .min(2, "Bank Account Type is required")
    .max(50, "Bank Account Type is too long"),
  bankAccountHolderName: z
    .string()
    .min(2, "Bank Account Holder Name is required")
    .max(100, "Bank Account Holder Name is too long"),
});

// Skeleton component for bank details view
const BankDetailsSkeleton = () => {
  return (
    <Box elevation={3} sx={{ p: 3, mb: 4 }}>
      <Skeleton variant="text" width="40%" height={60} sx={{ mb: 3 }} />

      <Grid container spacing={2}>
        {[...Array(6)].map((_, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Skeleton variant="text" width="60%" height={30} sx={{ mb: 4 }} />
            <Skeleton variant="text" width="80%" height={24} />
          </Grid>
        ))}
      </Grid>

      <Divider sx={{ my: 3 }} />

      <Box sx={{ display: "flex", justifyContent: "center" }}>
        <Skeleton
          variant="rectangular"
          width={250}
          height={40}
          sx={{ borderRadius: 1 }}
        />
      </Box>
    </Box>
  );
};

// Skeleton component for bank details form
const BankingFormSkeleton = () => {
  return (
    <Box sx={{ position: "relative" }}>
      <Skeleton variant="text" width="40%" height={60} sx={{ mb: 4 }} />

      <Grid container spacing={3}>
        {[...Array(6)].map((_, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Skeleton variant="text" width="60%" height={30} sx={{ mb: 1 }} />
            <Skeleton
              variant="rectangular"
              width="100%"
              height={56}
              sx={{ borderRadius: 1 }}
            />
          </Grid>
        ))}
      </Grid>

      <Box
        sx={{ display: "flex", justifyContent: "center", width: "100%", mt: 4 }}
      >
        <Skeleton
          variant="rectangular"
          width={120}
          height={48}
          sx={{ borderRadius: 1 }}
        />
      </Box>
    </Box>
  );
};

const ClubBankingDetails = () => {
  const [bankDetails, setBankDetails] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [loading, setLoading] = useState(true);
  const [updateRequestOpen, setUpdateRequestOpen] = useState(false);
  const toast = UseToast();

  const fetchBankDetails = async () => {
    try {
      setLoading(true);
      const response = await Client.get("/club/profile/bankdetails");
      if (response.status === 204) {
        setEditMode(true);
        toast.info("You haven't added your bank details yet.");
        return;
      }
      if (!response.data.success) {
        toast.info(response.data.message || "Please try again sometime later.");
        return;
      }
      if (response.data.data.isLocked === false) {
        setEditMode(true);
        return;
      }
      setBankDetails(response.data.data);
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || "Failed to fetch bank details";
      toast.error(errorMessage);
      console.error("Error fetching bank details:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBankDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleUpdateRequest = () => {
    setUpdateRequestOpen(true);
  };

  const handleCloseUpdateRequest = () => {
    setUpdateRequestOpen(false);
  };

  return (
    <Box sx={{ maxWidth: "lg", margin: "auto", my: 8, p: 4 }}>
      <BackButton />
      {loading && !editMode && <BankDetailsSkeleton />}

      {loading && editMode && <BankingFormSkeleton />}

      {!loading && !editMode && bankDetails && (
        <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
          <Typography variant="h4" sx={{ mb: 3 }}>
            Bank Details
          </Typography>

          <Alert severity="info" sx={{ mb: 3, fontSize: 16 }}>
            Bank details can only be updated with admin approval. If you need to
            update your bank details, please submit an update request.
          </Alert>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ fontWeight: "semibold" }}>
                Account Holder Name
              </Typography>
              <Typography variant="body1">
                {bankDetails.bankAccountHolderName}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ fontWeight: "semibold" }}>
                Bank Name
              </Typography>
              <Typography variant="body1">{bankDetails.bankName}</Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ fontWeight: "semibold" }}>
                Account Number
              </Typography>
              <Typography variant="body1">
                {bankDetails.AccountNumber}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ fontWeight: "semibold" }}>
                Branch Name
              </Typography>
              <Typography variant="body1">{bankDetails.branchName}</Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ fontWeight: "semibold" }}>
                Account Type
              </Typography>
              <Typography variant="body1">
                {bankDetails.bankAccountType}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ fontWeight: "semibold" }}>
                IFSC Code
              </Typography>
              <Typography variant="body1">
                {bankDetails.branchIFSCCode}
              </Typography>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Box sx={{ display: "flex", justifyContent: "center" }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleUpdateRequest}
              sx={{
                fontSize: 16,
                px: 2,
                bgcolor: "hsla(120, 49%, 35%, 1)",
                "&:hover": {
                  bgcolor: "rgb(39, 104, 39)",
                },
              }}
            >
              Request Bank Details Update
            </Button>
          </Box>
        </Paper>
      )}

      {!loading && editMode && (
        <ClubBankingForm
          editMode={editMode}
          isLoading={false}
          onSuccess={fetchBankDetails}
        />
      )}

      <BankDetailsUpdateRequest
        open={updateRequestOpen}
        onClose={handleCloseUpdateRequest}
      />
    </Box>
  );
};

export default ClubBankingDetails;

const ClubBankingForm = ({
  editMode = false,
  isLoading = false,
  onSuccess,
}) => {
  const toast = UseToast();
  const [submitting, setSubmitting] = useState(false);
  const { control, handleSubmit, reset } = useForm({
    resolver: zodResolver(bankDetailsSchema),
    defaultValues: {
      bankName: "",
      AccountNumber: "",
      branchIFSCCode: "",
      branchName: "",
      bankAccountType: "",
      bankAccountHolderName: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (data) => {
    try {
      if (!editMode) return;

      setSubmitting(true);
      toast.info("Submitting bank details...");

      const response = await Client.post("/club/profile/bankdetails", data);
      if (response.status === 422) {
        toast.error("Validation failed. Please check your input.");
        return;
      }
      if (response.status === 403) {
        toast.error(
          "Forbidden. You don't have permission to perform this action."
        );
        return;
      }
      if (response.status === 400) {
        if (response.data.error === "Invalid bank account details") {
          toast.error(
            "Invalid bank account details. Please verify and try again."
          );
        } else if (response.data.error === "Bank account verification failed") {
          toast.error(
            "Bank account verification failed. Please check your details and try again."
          );
        } else {
          toast.error(
            response.data.error ||
              "An error occurred while processing your request."
          );
        }
        return;
      }
      if (!response.data.success) {
        toast.error(
          response.data.error ||
            "An error occurred while processing your request."
        );
        return;
      }
      toast.success("Bank details added successfully");
      reset();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error in onSubmit:", error);
      toast.error("Failed to add bank details. Please try again later.");
    } finally {
      setSubmitting(false);
    }
  };

  // If loading, show skeleton
  if (isLoading) {
    return <BankingFormSkeleton />;
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Grid
        container
        spacing={3}
        sx={{
          ".MuiFormControl-root": { mt: "4px !important" },
          ".MuiInputBase-input": { py: 1.5 },
          ".MuiAutocomplete-input": { p: "4px !important" },
          ".MuiGrid-root .MuiGrid-item": { pt: "0px !important" },
          position: "relative",
        }}
      >
        {submitting && (
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1000,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              width: "100%",
              height: "100%",
              backgroundColor: "rgba(255, 255, 255, 0.5)",
            }}
          >
            <Spinner />
          </Box>
        )}

        <Grid item xs={12} sx={{ mt: 2 }}>
          <Typography variant="h4" sx={{ mb: 4, textDecoration: "underline" }}>
            Banking Details
          </Typography>
        </Grid>

        {/* Banking Details */}

        <Grid item xs={12} md={6}>
          <FormTextField
            name="bankAccountHolderName"
            control={control}
            title="Bank Account Holder Name"
            placeholder="Enter Bank Account Holder Name"
            required
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="bankName"
            control={control}
            title="Bank Name"
            placeholder="Enter Bank Name"
            required
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormNumberField
            name="AccountNumber"
            minLength={9}
            maxLength={18}
            control={control}
            title="Account Number"
            placeholder="Enter Account Number"
            required
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="branchName"
            control={control}
            title="Branch Name"
            placeholder="Enter Branch Name"
            required
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="bankAccountType"
            control={control}
            title="Bank Account Type"
            placeholder="Enter Bank Account Type"
            required
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormTextField
            name="branchIFSCCode"
            control={control}
            title="Branch IFSC Code"
            placeholder="Enter Branch IFSC Code"
            required
          />
        </Grid>

        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            width: "100%",
            mt: 4,
          }}
        >
          <Button
            type="submit"
            variant="contained"
            disabled={submitting}
            sx={{
              fontSize: 16,
              px: 2,
              bgcolor: "hsla(120, 49%, 35%, 1)",
              "&:hover": {
                bgcolor: "rgb(39, 104, 39)",
              },
            }}
            size="large"
          >
            {submitting ? "Submitting..." : "Create"}
          </Button>
        </Box>
      </Grid>
    </form>
  );
};
