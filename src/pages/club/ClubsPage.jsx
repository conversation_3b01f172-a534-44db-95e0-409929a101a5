import React, { useState, useEffect, lazy } from "react";
import { <PERSON>, Button, Container } from "@mui/material";

import { Client } from "../../api/client";

import { Link } from "react-router-dom";
import DynamicTable from "../../components/common/DynamicTable";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { ArrowBack } from "@mui/icons-material";
import BackButton from "../../components/common/BackButton";

import ClubSearchForm from "../../components/common/ClubSearchForm";

const ClubsPage = () => {
  // States for form inputs
  const [search, setSearch] = useState({
    clubName: "",
    country: "",
    state: "",
    district: "",
    city: "",
  });

  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 10;
  const [clubs, setClubs] = useState([]);
  const toast = UseToast();

  // Define fetchClubs function without useCallback to avoid dependency issues
  const fetchClubs = async () => {
    setLoading(true);

    try {
      const response = await Client.get("/club", {
        params: { ...search, page, limit },
      });
      if (response.status === 204) {
        setClubs([]);
        toast.info("No clubs found");
        return;
      }
      const { clubs, currentPage: cp, totalPages: tp } = response.data.data;
      setTotalPages(tp || 1);
      setPage(cp || 1);
      setClubs(clubs);
    } catch (error) {
      if (error instanceof AxiosError && error.response?.status === 422) {
        console.error("Validation error:", error.response.data);
        toast.info("Please correct the search criteria");
      } else {
        console.error("Error fetching clubs:", error);
        toast.error("Failed to fetch clubs. Please try again.");
      }
      setClubs([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search button click
  const handleSearch = (newpage) => {
    setPage(newpage);
    fetchClubs(newpage);
  };
  const handleReset = () => {
    setSearch({ clubName: "", country: "", state: "", district: "", city: "" });
    setPage(1);
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchClubs(newPage);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 2, pb: 8 }}>
      <BackButton />
      {/* Search Form */}
      <ClubSearchForm
        search={search}
        setSearch={setSearch}
        handleSearch={handleSearch}
        loading={loading}
        handleReset={handleReset}
      />

      {/* Clubs Table */}
      <DynamicTable
        columns={[
          {
            id: "index",
            label: "S No",
            width: "80px",
            format: (_, item, index) => (page - 1) * limit + index + 1,
          },
          { id: "clubName", label: "Club Name" },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
          { id: "district", label: "District" },
          { id: "city", label: "City" },
        ]}
        data={clubs}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        detailsPath="/clubs/"
        idField="clubId"
    
        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />
    </Container>
  );
};

export default ClubsPage;
