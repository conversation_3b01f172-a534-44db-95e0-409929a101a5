import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  TextField,
  MenuItem,
  Chip,
} from "@mui/material";
import { Search as SearchIcon } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const ClubTournamentsPage = () => {
  const [tournaments, setTournaments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(1);

  // States for form inputs
  const [search, setSearch] = useState(() => {
    const statusFromURL = new URLSearchParams(window.location.search).get(
      "status"
    );
    return {
      status: statusFromURL || "all",
      title: "",
    };
  });
  const [page, setPage] = useState(1);
  const limit = 10; // Adjust as needed
  const toast = UseToast();

  const handlePageChange = (_, value) => {
    setPage(value);
    if (value === page) return;
    fetchTournaments(value);
  };

  const handleSearch = () => {
    fetchTournaments(1);
  };

  const fetchTournaments = useCallback(
    async (pageNumber) => {
      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
        };

        // Only add non-empty search parameters
        Object.keys(search).forEach((key) => {
          if (search[key] !== "" && search[key] !== "all") {
            params[key] = search[key];
          }
        });

        // In a real application, you would use a specific endpoint for club tournaments
        // For example: /club/tournaments or /tournaments/club/:clubId
        const response = await Client.get("/tournament/club", { params });

        if (!response.data.success) {
          toast.info("No tournaments found");
          setTournaments([]);
          setTotalPages(0);
          return;
        }

        if (response.data.data.total === 0) {
          toast.info("No tournaments found");
          setTournaments([]);
          setTotalPages(0);
          return;
        }

        setTournaments(response.data.data.tournaments);
        setTotalPages(response.data.data.totalPages);
      } catch (error) {
        if (error instanceof AxiosError && error.response) {
          toast.info(error.response.data.error);
          return;
        }
        console.error("Error fetching tournaments:", error);
        toast.info("An error occurred. Please try again later.");
      } finally {
        setLoading(false);
      }
    },
    [limit, search]
  );

  // Initial fetch of tournaments
  useEffect(() => {
    fetchTournaments(1);
  }, []);

  const getStatusChip = (status) => {
    switch (status) {
      case "in-progress":
        return <Chip label="In Progress" color="primary" size="small" />;
      case "upcoming":
        return <Chip label="Upcoming" color="secondary" size="small" />;
      case "completed":
        return <Chip label="Completed" color="default" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "-";

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "-";

      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "2-digit",
        year: "numeric",
      });
    } catch {
      return "-";
    }
  };

  return (
    <Container
      maxWidth="xl"
      sx={{
        py: 4,
        pb: 8,
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <BackButton />
      {/* Search Form */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f9f9f9", borderRadius: 2, boxShadow: 3 }}
      >
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              select
              label="Status"
              variant="outlined"
              fullWidth
              value={search.status}
              onChange={(e) => setSearch({ ...search, status: e.target.value })}
              sx={{
                bgcolor: "white",
                "& .MuiFormLabel-root": { color: "black" },
              }}
            >
              <MenuItem value="all">All Tournaments</MenuItem>
              <MenuItem value="in-progress">In Progress</MenuItem>
              <MenuItem value="upcoming">Upcoming</MenuItem>
              <MenuItem value="completed">Completed</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              label="Tournament Title"
              variant="outlined"
              fullWidth
              value={search.title}
              onChange={(e) => setSearch({ ...search, title: e.target.value })}
              sx={{
                bgcolor: "white",
                "& .MuiFormLabel-root": { color: "black" },
              }}
              placeholder="Enter tournament title"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleSearch}
              startIcon={<SearchIcon />}
              sx={{
                bgcolor: "#2c2891",
                textTransform: "none",
                height: "56px",
                fontSize: "16px",

                "&:hover": {
                  bgcolor: "#1a1b60",
                },
              }}
            >
              Search
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Tournaments Table */}
      <DynamicTable
        columns={[
          {
            id: "index",
            label: "S No",
            width: "80px",
            format: (_, __, index) => (page - 1) * limit + index + 1,
          },
          {
            id: "title",
            label: "Tournament Name",
            format: (value) => (
              <Box sx={{ textTransform: "capitalize" }}>
                {value.toLowerCase().replace(/-/g, " ")}
              </Box>
            ),
          },
          {
            id: "startDate",
            label: "Start Date",
            format: (value) => formatDate(value),
          },
          {
            id: "endDate",
            label: "End Date",
            format: (value) => formatDate(value),
          },
          {
            id: "registrationEndDate",
            label: "Registration End",
            format: (value) => formatDate(value),
          },
          {
            id: "city",
            label: "Location",
          },
        ]}
        data={tournaments}
        showBrouchureButton={false}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        detailsPath="/dashboard/tournaments/"
        idField="title"
 
        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />
    </Container>
  );
};

export default ClubTournamentsPage;
