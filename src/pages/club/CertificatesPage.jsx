import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useParams } from "react-router-dom";
import { PDFViewer, PDFDownloadLink } from "@react-pdf/renderer";
import {
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Box,
  CircularProgress,
  Alert,
} from "@mui/material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import BackButton from "../../components/common/BackButton";
import CertificateTemplate from "../../components/certificates/CertificateTemplate";
// import CertificateTemplate2 from "../../components/common/CertificateTemplate2";
// import CertificateTemplate3 from "../../components/common/CertificateTemplate3";


// Validate template components on import
const validateTemplateComponent = (component, name) => {
  if (!component || typeof component !== "function") {
    console.error(`Template component ${name} is not valid:`, component);
    return false;
  }
  return true;
};

const CERTIFICATE_TEMPLATES = {
  1: {
    name: "Template 1",
    description: "template with trophy on background",
    component: CertificateTemplate,
    isValid: validateTemplateComponent(
      CertificateTemplate,
      "CertificateTemplate"
    ),
  },
  // 2: {
  //   name: "Template 2",
  //   description: "Vibrant modern design with orange accents",
  //   component: CertificateTemplate2,
  //   isValid: validateTemplateComponent(
  //     CertificateTemplate2,
  //     "CertificateTemplate2"
  //   ),
  // },
  // 3: {
  //   name: "Template 3",
  //   description: "Sophisticated design with brown borders",
  //   component: CertificateTemplate3,
  //   isValid: validateTemplateComponent(
  //     CertificateTemplate3,
  //     "CertificateTemplate3"
  //   ),
  // },
};

const CertificatesPage = () => {
  const { title } = useParams();

  const toast = UseToast();

  const [tournament, setTournament] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState(1);
  const [certificateData, setCertificateData] = useState({
    organization: "Sample Organization Name",
    tournamentTitle: "Sample Tournament",
    subtitle: "Sample Tournament Description",
    poweredBy: "ChessBrigade.com",
    sponsorName: "",
    playerName: "Sample Player Name",
    tournamentDate: new Date().toLocaleDateString(),
    venue: "Sample Venue, Sample City",
    points: "5",
    totalRounds: "7",
    category: "U12 Boys",
    position: "1st Place",
    timestamp: new Date().toLocaleDateString(),
  });
  const [pdfData, setPdfData] = useState(certificateData);
  const [isClient, setIsClient] = useState(false);
  const [saving, setSaving] = useState(false);
  const [templateSwitching, setTemplateSwitching] = useState(false);
  const [pdfError, setPdfError] = useState(null);
  const [pdfKey, setPdfKey] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const fetchTournamentDetails = useCallback(async () => {
    try {
      setLoading(true);
      const encodedTitle = encodeURIComponent(title);
      const response = await Client.get(`/tournament/${encodedTitle}`);

      if (response.data.success) {
        const tournamentData = response.data.data;
        setTournament(tournamentData);
        console.log("Tournament Data:", tournamentData.certificateData);
        if (tournamentData.certificateData) {
          setSelectedTemplate(tournamentData.certificateData.templateId);
          setCertificateData(tournamentData.certificateData);
          setPdfData(tournamentData.certificateData);
        } else {
          const initialData = {
            ...certificateData,
            organization: tournamentData.organizerName || "",
            tournamentTitle: tournamentData.title.replace(/-/g, " ") || "",
            subtitle: tournamentData.description || "",
            tournamentDate:
              new Date(tournamentData.startDate).toLocaleDateString() || "",
            venue: `${tournamentData.venueAddress || ""}, ${
              tournamentData.city || ""
            }`,
          };

          setCertificateData(initialData);
          setPdfData(initialData); // Initialize PDF data too
        }
      } else {
        toast.error("Failed to fetch tournament details");
      }
    } catch (error) {
      console.error("Error fetching tournament details:", error);
      toast.error("Error fetching tournament details");
    } finally {
      setLoading(false);
    }
  }, [title, toast]);

  const handleManualRefresh = useCallback(() => {
    setRefreshing(true);
    setPdfError(null);

    // Update PDF data with current form data
    setPdfData({ ...certificateData });
    setPdfKey((prev) => prev + 1);

    // Show refreshing state briefly
    setTimeout(() => {
      setRefreshing(false);
    }, 500);
  }, [certificateData]);

  useEffect(() => {
    setIsClient(true);
    fetchTournamentDetails();
  }, [title]);

  // Clear errors when template changes successfully
  useEffect(() => {
    if (!templateSwitching && selectedTemplate) {
      // Clear any previous errors after a successful template switch
      const timer = setTimeout(() => {
        setPdfError(null);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [selectedTemplate]);
  const hasUnsavedChanges = useMemo(() => {
    return JSON.stringify(certificateData) !== JSON.stringify(pdfData);
  }, [certificateData, pdfData]);

  // Global error handler for PDF-related errors
  useEffect(() => {
    const handleError = (event) => {
      if (
        event.error &&
        (event.error.message?.includes("Eo is not a function") ||
          event.error.message?.includes("PDF") ||
          event.error.stack?.includes("reconciler") ||
          event.error.stack?.includes("PDFDownloadLink") ||
          event.error.stack?.includes("PDFViewer"))
      ) {
        console.error("PDF-related error caught:", event.error);
        setPdfError(
          "PDF rendering error occurred. Please try refreshing the preview."
        );
        event.preventDefault();
        return false;
      }
    };

    const handleUnhandledRejection = (event) => {
      if (
        event.reason &&
        typeof event.reason === "object" &&
        (event.reason.message?.includes("PDF") ||
          event.reason.message?.includes("Eo is not a function") ||
          event.reason.stack?.includes("reconciler"))
      ) {
        console.error("PDF-related promise rejection:", event.reason);
        setPdfError(
          "PDF rendering error occurred. Please try refreshing the preview."
        );
        event.preventDefault();
        return false;
      }
    };

    window.addEventListener("error", handleError);
    window.addEventListener("unhandledrejection", handleUnhandledRejection);

    return () => {
      window.removeEventListener("error", handleError);
      window.removeEventListener(
        "unhandledrejection",
        handleUnhandledRejection
      );
    };
  }, []);

  const handleInputChange = useCallback((field, value) => {
    setCertificateData((prev) => ({
      ...prev,
      [field]: value,
    }));
    // PDF will NOT update here - only on manual refresh
  }, []);
  const handleTemplateChange = useCallback(
    (newTemplateId) => {
      setSelectedTemplate(newTemplateId);
      // Also update PDF data when template changes
      setPdfData({ ...certificateData });
      setPdfKey((prev) => prev + 1);
      setPdfError(null);
    },
    [certificateData]
  );

  const handleSaveCertificateConfig = async () => {
    try {
      setSaving(true);
      const encodedTitle = encodeURIComponent(title);

      const configData = {
        templateId: selectedTemplate,
        certificateData: certificateData, // Save current form data
        tournamentTitle: title,
      };

      const response = await Client.patch(
        `/tournament/${encodedTitle}/certificate-config`,
        configData
      );

      if (response.data.success) {
        toast.success("Certificate configuration saved successfully!");
      } else {
        toast.error("Failed to save certificate configuration");
      }
    } catch (error) {
      console.error("Error saving certificate config:", error);
      toast.error("Error saving certificate configuration");
    } finally {
      setSaving(false);
    }
  };

  const handleGenerateCertificate = async () => {
    try {
      const encodedTitle = encodeURIComponent(title);

      const generateData = {
        templateId: selectedTemplate,
        certificateData: certificateData,
      };

      const response = await Client.post(
        `/tournament/${encodedTitle}/generate-certificate`,
        generateData
      );

      if (response.data.success) {
        toast.success("Certificate generated successfully!");
        // You can add additional logic here like downloading the certificate
      } else {
        toast.error("Failed to generate certificate");
      }
    } catch (error) {
      console.error("Error generating certificate:", error);
      toast.error(error.response.data.error || "Error generating certificate");
    }
  };

  const SelectedTemplateComponent =
    CERTIFICATE_TEMPLATES[selectedTemplate]?.component ||
    CertificateTemplate;

  // Debug logging
  console.log("Selected template:", selectedTemplate);
  console.log("Template component:", SelectedTemplateComponent);
  console.log("Certificate data:", certificateData);
  console.log("Template switching:", templateSwitching);
  console.log("PDF error:", pdfError);




  // Validate template component
  const currentTemplate = CERTIFICATE_TEMPLATES[selectedTemplate];
  const isValidTemplate =
    currentTemplate &&
    currentTemplate.isValid &&
    SelectedTemplateComponent &&
    typeof SelectedTemplateComponent === "function";

  // Safe PDF component wrapper
  const SafePDFComponent = useMemo(() => {
    const currentTemplate = CERTIFICATE_TEMPLATES[selectedTemplate];
    const SelectedTemplateComponent = currentTemplate?.component;

    if (!SelectedTemplateComponent || !currentTemplate?.isValid) {
      return null;
    }

    try {
      // Use pdfData instead of certificateData
      return <SelectedTemplateComponent data={pdfData} />;
    } catch (error) {
      console.error("PDF component creation error:", error);
      setPdfError(error.message);
      return null;
    }
  }, [selectedTemplate, pdfData, pdfKey]);

  if (loading) {
    return (
      <Container
        maxWidth="xl"
        sx={{ py: 4, display: "flex", justifyContent: "center" }}
      >
        <CircularProgress />
      </Container>
    );
  }

  if (!isClient) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Typography>Loading PDF Preview...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <BackButton />

      <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
        Certificate Templates - {tournament?.title}
      </Typography>

      <Grid container spacing={3}>
        {/* Left Panel - Configuration */}
        <Grid item xs={12} md={4}>
          <Paper elevation={3} sx={{ p: 3, height: "fit-content" }}>
            <Typography variant="h6" gutterBottom>
              Certificate Configuration
            </Typography>

            {/* Template Selection */}
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Certificate Template</InputLabel>
              <Select
                value={selectedTemplate}
                onChange={(e) => handleTemplateChange(parseInt(e.target.value))}
                label="Certificate Template"
                // Remove the disabled prop
              >
                {Object.entries(CERTIFICATE_TEMPLATES)
                  // Temporarily remove the filter to see all templates
                  // .filter(([, template]) => template.isValid)
                  .map(([id, template]) => (
                    <MenuItem key={id} value={parseInt(id)}>
                      <Box>
                        <Typography variant="body1">{template.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {template.description}
                        </Typography>
                        {/* Add validation status for debugging */}
                        <Typography
                          variant="caption"
                          color={
                            template.isValid ? "success.main" : "error.main"
                          }
                        >
                          {template.isValid ? " ✓ Valid" : " ✗ Invalid"}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>

            {/* Template switching indicator
            {templateSwitching && (
              <Alert severity="info" sx={{ mb: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <CircularProgress size={16} />
                  <Typography variant="body2">Switching template...</Typography>
                </Box>
              </Alert>
            )} */}
            {hasUnsavedChanges && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="body2" sx={{ fontSize: "0.875rem" }}>
                  You have unsaved changes. Click "Refresh Preview" to see
                  updates in the PDF.
                </Typography>
              </Alert>
            )}

            {/* Error display */}
            {pdfError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                <Typography variant="body2">{pdfError}</Typography>
                <Button
                  size="small"
                  onClick={handleManualRefresh}
                  sx={{ mt: 1 }}
                >
                  Retry
                </Button>
              </Alert>
            )}

            {/* Certificate Details Form */}
            <TextField
              fullWidth
              label="Organization Name"
              value={certificateData.organization}
              onChange={(e) =>
                handleInputChange("organization", e.target.value)
              }
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Tournament Title"
              value={certificateData.tournamentTitle}
              onChange={(e) =>
                handleInputChange("tournamentTitle", e.target.value)
              }
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Subtitle"
              value={certificateData.subtitle}
              onChange={(e) => handleInputChange("subtitle", e.target.value)}
              sx={{ mb: 2 }}
            />

            {/* <TextField
              fullWidth
              label="Sponsor Name (Optional)"
              value={certificateData.sponsorName}
              onChange={(e) => handleInputChange("sponsorName", e.target.value)}
              sx={{ mb: 2 }}
            /> */}

            <TextField
              fullWidth
              label="Tournament Date"
              value={certificateData.tournamentDate}
              onChange={(e) =>
                handleInputChange("tournamentDate", e.target.value)
              }
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Venue"
              multiline
              rows={2}
              value={certificateData.venue}
              onChange={(e) => handleInputChange("venue", e.target.value)}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Powered By"
              value={certificateData.poweredBy}
              onChange={(e) => handleInputChange("poweredBy", e.target.value)}
              sx={{ mb: 3 }}
            />

            {/* Action Buttons */}
            <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
              <Button
                variant="outlined"
                onClick={handleSaveCertificateConfig}
                disabled={saving}
                startIcon={saving ? <CircularProgress size={20} /> : null}
              >
                {saving ? "Saving..." : "Save Configuration"}
              </Button>

              <Button
                variant="contained"
                onClick={handleGenerateCertificate}
                color="primary"
              >
                Generate Certificate
              </Button>

              {isValidTemplate && !pdfError && SafePDFComponent ? (
                <PDFDownloadLink
                  key={`pdf-download-${pdfKey}`}
                  document={SafePDFComponent}
                  fileName={`certificate_template_${selectedTemplate}.pdf`}
                  style={{ textDecoration: "none" }}
                >
                  {({ loading, error }) => (
                    <Button
                      variant="contained"
                      color="success"
                      disabled={loading || templateSwitching || error}
                      fullWidth
                    >
                      {error
                        ? "Download Error"
                        : loading
                        ? "Preparing..."
                        : templateSwitching
                        ? "Switching..."
                        : "Download Preview"}
                    </Button>
                  )}
                </PDFDownloadLink>
              ) : (
                <Button variant="contained" color="success" disabled fullWidth>
                  Download Unavailable
                </Button>
              )}
            </Box>
          </Paper>
        </Grid>

        {/* Right Panel - Preview */}
        <Grid item xs={12} md={8}>
          <Paper elevation={3} sx={{ p: 2, height: "80vh" }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
              }}
            >
              <Typography variant="h6">
                Live Preview - {CERTIFICATE_TEMPLATES[selectedTemplate]?.name}
              </Typography>

              {/* SOLUTION 9: Prominent refresh button */}
              <Button
                variant={hasUnsavedChanges ? "contained" : "outlined"}
                color={hasUnsavedChanges ? "primary" : "inherit"}
                onClick={handleManualRefresh}
                disabled={refreshing}
                startIcon={refreshing ? <CircularProgress size={16} /> : null}
              >
                {refreshing
                  ? "Refreshing..."
                  : hasUnsavedChanges
                  ? "Refresh Preview"
                  : "Refresh"}
              </Button>
            </Box>

            <Box sx={{ height: "calc(100% - 40px)", border: "1px solid #ddd" }}>
              {refreshing ? (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                    flexDirection: "column",
                    gap: 2,
                  }}
                >
                  <CircularProgress />
                  <Typography variant="body2" color="text.secondary">
                    Updating preview...
                  </Typography>
                </Box>
              ) : pdfError ? (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                    flexDirection: "column",
                    gap: 2,
                  }}
                >
                  <Typography color="error" variant="h6">
                    PDF Rendering Error
                  </Typography>
                  <Typography color="error" variant="body2">
                    {pdfError}
                  </Typography>
                  <Button variant="outlined" onClick={handleManualRefresh}>
                    Retry
                  </Button>
                </Box>
              ) : SafePDFComponent ? (
                <PDFViewer
                  key={`pdf-viewer-${selectedTemplate}-${pdfKey}`}
                  style={{ width: "100%", height: "100%" }}
                  showToolbar={true}
                >
                  {SafePDFComponent}
                </PDFViewer>
              ) : (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                  }}
                >
                  <Typography color="error">
                    Template not found or invalid
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default CertificatesPage;