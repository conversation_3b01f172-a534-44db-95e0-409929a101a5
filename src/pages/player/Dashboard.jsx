import BankBuildingIcon from "@mui/icons-material/AccountBalance";
import PersonalTrainerIcon from "@mui/icons-material/FitnessCenter";
import AddUserMaleIcon from "@mui/icons-material/PersonAdd";
import PaymentHistoryIcon from "@mui/icons-material/Receipt";
import TrainingIcon from "@mui/icons-material/SportsHandball";
import useGlobalContext from "../../lib/hooks/UseGlobalContext";
import { Box, Stack, Typography, CircularProgress } from "@mui/material";
import React, { useEffect, useState } from "react";

import LinkItemDashboard from "../../components/common/LinkItemDashboard";
import {
  ChatBubble,
  Extension,
  Gamepad,
  Notifications,
  InsertDriveFile,
} from "@mui/icons-material";
import UpcomingTournaments from "../../components/common/UpcomingTournaments";
import { Client } from "../../api/client";

// Dashboard card data for mapping
const dashboardCards = [
  {
    title: "My Profile",
    color: "hsla(276, 100%, 90%, 1)",
    icon: <AddUserMaleIcon />,
    link: "profile",
  },
  {
    title: "Notifications",
    color: "#FFD1DC",
    icon: <Notifications />,
    link: "notifications",
  },
  {
    title: "My Docs",
    color: "hsla(35, 93%, 73%, 1)",
    icon: <InsertDriveFile />,
    link: "my-docs",
  },
  {
    title: "My Certificates",
    color: "#F5F54647",
    icon: <BankBuildingIcon />,
    link: "my-certificate",
  },
  {
    title: "My Chats",
    color: "#BEDDF0",
    icon: <ChatBubble />,
    link: "my-chats",
  },
  {
    title: "Solve Puzzles",
    color: "#D2E8A8",
    icon: <Extension />,
    link: "solve-puzzles",
  },
  {
    title: "Play Online",
    color: "#9ED4CE",
    icon: <Gamepad />,
    link: "pay-online",
  },
  {
    title: "Payments",
    color: "#F0D7D7",
    icon: <PaymentHistoryIcon />,
    link: "payments-history",
  },
  {
    title: "Game History",
    color: "#7868617D",
    icon: <TrainingIcon />,
    link: "history",
  },
  {
    title: "Tournament History",
    color: "#4641E557",
    icon: <PersonalTrainerIcon />,
    link: "tournament-history",
  },
];

const PlayerDashBoard = () => {
  const { user, currentProfile, fetchProfileData, authLoading } =
    useGlobalContext();
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = useState([]);

  const getAd = async () => {
    setLoading(true);
    try {
      const response = await Client.get("/user/ad/player");
      if (response.data.success) {
        setData(response.data.data);
      }
    } catch (e) {
      console.error("error while get ad", e);
    } finally {
      setLoading(false);
    }
  };
  // Fetch profile data if not already available
  // Fetch profile data if not already available
  useEffect(() => {
    const getProfileData = async () => {
      if (user && !currentProfile && !authLoading) {
        setLoading(true);
        try {
          await fetchProfileData();
        } catch (error) {
          console.error("Error fetching club profile:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    getProfileData();
    getAd();
  }, [user, currentProfile, authLoading]);

  return (
    <Box sx={{ px: "5vw", py: "5vh", maxWidth: "100%" }}>
      {loading ? (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          height="50vh"
        >
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Stack>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Welcome, {user?.name || ""}!
            </Typography>
            {user?.cbid && (
              <Typography variant="h4" gutterBottom sx={{ mt: 2 }}>
                CBID: {user?.cbid}
              </Typography>
            )}
          </Stack>

          <Typography variant="h4" gutterBottom sx={{ mt: 2 }}>
            My Dashboard
          </Typography>

          <LinkItemDashboard links={dashboardCards} Data={data} />
          <UpcomingTournaments club={false} />
        </>
      )}
    </Box>
  );
};

export default PlayerDashBoard;
