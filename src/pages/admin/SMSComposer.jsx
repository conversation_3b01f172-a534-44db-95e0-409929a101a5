import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  TextField,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
} from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../../components/common/BackButton";
import TemplateSelector from "../../components/admin/TemplateSelector";
import MessagePreview from "../../components/admin/MessagePreview";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const SMSComposer = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = UseToast();

  const { 
    selectedUsers = [], 
    selectedType = "", 
    mode = "sms",
    isBulkSend = false,
    searchFilters = {}
  } = location.state || {};

  const [smsData, setSmsData] = useState({
    content: "",
    templateId: "",
    useTemplate: false,
  });

  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [templateVariables, setTemplateVariables] = useState({});
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [bulkSending, setBulkSending] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [bulkConfirmDialog, setBulkConfirmDialog] = useState(false);

  useEffect(() => {
    if (selectedUsers.length === 0 && !isBulkSend) {
      toast.error("No recipients selected");
      navigate("/dashboard/sms");
      return;
    }
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    setLoading(true);
    try {
      const response = await Client.get("/admin/sms-templates");
      if (response.data.success) {
        const groupedTemplates = response.data.data.templates || {};

        // Flatten the object into a single array
        const flatTemplates = Object.entries(groupedTemplates).flatMap(([category, items]) => 
          items.map(template => ({ ...template, category }))
        );

        setTemplates(flatTemplates);
      }
    } catch (error) {
      console.error("Error fetching templates:", error);
      toast.error("Failed to load SMS templates");
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template) => {
    if (template) {
      setSelectedTemplate(template);
      setSmsData(prev => ({
        ...prev,
        content: template.content || "",
        templateId: template.templateId,
        useTemplate: true,
      }));

      // Initialize template variables with empty values
      if (template.variables && template.variables.length > 0) {
        const initialVariables = {};
        template.variables.forEach(variable => {
          initialVariables[variable] = "";
        });
        setTemplateVariables(initialVariables);
      } else {
        setTemplateVariables({});
      }
    } else {
      setSelectedTemplate(null);
      setSmsData(prev => ({
        ...prev,
        content: "",
        templateId: "",
        useTemplate: false,
      }));
      setTemplateVariables({});
    }
  };

  const handleVariableChange = (variableName, value) => {
    setTemplateVariables(prev => ({
      ...prev,
      [variableName]: value
    }));

    // Update the SMS content with replaced variables
    updateSMSContentWithVariables(variableName, value);
  };

  const updateSMSContentWithVariables = (changedVariable = null, changedValue = null) => {
    if (!selectedTemplate || !selectedTemplate.content) return;

    let content = selectedTemplate.content;
    const currentVariables = changedVariable 
      ? { ...templateVariables, [changedVariable]: changedValue }
      : templateVariables;

    // Replace variables in the template content
    Object.entries(currentVariables).forEach(([variable, value]) => {
      const regex = new RegExp(`\\{\\{${variable}\\}\\}`, 'g');
      content = content.replace(regex, value || `{{${variable}}}`);
    });

    setSmsData(prev => ({
      ...prev,
      content: content
    }));
  };

  const validateSMS = () => {
    if (!smsData.content.trim()) {
      toast.error("SMS content is required");
      return false;
    }
    if (smsData.content.length > 1600) {
      toast.error("SMS content is too long (maximum 1600 characters)");
      return false;
    }

    // Check if all required variables are filled
    if (selectedTemplate && selectedTemplate.variables) {
      const missingVariables = selectedTemplate.variables.filter(
        variable => !templateVariables[variable] || templateVariables[variable].trim() === ""
      );

      if (missingVariables.length > 0) {
        toast.error(`Please fill in all template variables: ${missingVariables.join(", ")}`);
        return false;
      }
    }

    return true;
  };

  // Regular send SMS (with recipient data)
  const handleSendSMS = async () => {
    if (!validateSMS()) return;

    setSending(true);
    try {
      const payload = {
        recipients: selectedUsers.map(user => ({
          id: user.id,
          phone: user.phone,
          name: user.name,
          type: user.type,
        })),
        content: smsData.content,
        templateId: smsData.useTemplate ? smsData.templateId : null,
        templateVariables: smsData.useTemplate ? templateVariables : {},
        recipientType: selectedType,
      };

      const response = await Client.post("/admin/send-sms", payload);

      if (response.data.success) {
        toast.success(`SMS sent successfully to ${selectedUsers.length} recipients`);
        navigate("/dashboard/sms");
      } else {
        toast.error(response.data.message || "Failed to send SMS");
      }
    } catch (error) {
      console.error("Error sending SMS:", error);
      toast.error("Failed to send SMS. Please try again.");
    } finally {
      setSending(false);
    }
  };

  // Bulk send SMS (without fetching all recipient data)
  const handleBulkSendSMS = async () => {
    if (!validateSMS()) return;

    setBulkSending(true);
    try {
      const payload = {
        content: smsData.content,
        templateId: smsData.useTemplate ? smsData.templateId : null,
        templateVariables: smsData.useTemplate ? templateVariables : {},
        recipientType: selectedType,
        searchFilters: searchFilters || {},
        bulkSend: true,
      };

      const response = await Client.post("/admin/send-bulk-sms", payload);

      if (response.data.success) {
        const sentCount = response.data.sentCount || "all matching";
        toast.success(`SMS sent successfully to ${sentCount} recipients`);
        navigate("/dashboard/sms");
      } else {
        toast.error(response.data.message || "Failed to send bulk SMS");
      }
    } catch (error) {
      console.error("Error sending bulk SMS:", error);
      toast.error("Failed to send bulk SMS. Please try again.");
    } finally {
      setBulkSending(false);
      setBulkConfirmDialog(false);
    }
  };

  const getRecipientSummary = () => {
    if (isBulkSend) {
      const typeLabel = selectedType.charAt(0).toUpperCase() + selectedType.slice(1);
      return `All ${typeLabel}s <Bulk Send>`;
    }

    const typeLabel = selectedType.charAt(0).toUpperCase() + selectedType.slice(1);
    return `${selectedUsers.length} ${typeLabel}${selectedUsers.length > 1 ? 's' : ''}`;
  };

  const getBulkSendDescription = () => {
    const filters = Object.entries(searchFilters || {})
      .filter(([key, value]) => value && value !== "")
      .map(([key, value]) => `${key}: ${value}`)
      .join(", ");

    return filters ? `Filtered by: ${filters}` : "No filters applied - all users";
  };

  const getCharacterCount = () => {
    return smsData.content.length;
  };

  const getSMSCount = () => {
    return Math.ceil(smsData.content.length / 160);
  };

  const getRecipientsWithPhone = () => {
    return selectedUsers.filter(user => user.phone);
  };

  const getRecipientsWithoutPhone = () => {
    return selectedUsers.filter(user => !user.phone);
  };

  const renderVariableInputs = () => {
    if (!selectedTemplate || !selectedTemplate.variables || selectedTemplate.variables.length === 0) {
      return null;
    }

    return (
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Template Variables
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Fill in the values for the template variables below:
        </Typography>

        <Grid container spacing={2}>
          {selectedTemplate.variables.map((variable, index) => (
            <Grid item xs={12} sm={6} key={variable}>
              <TextField
                label={`${variable.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}`}
                fullWidth
                value={templateVariables[variable] || ""}
                onChange={(e) => handleVariableChange(variable, e.target.value)}
                placeholder={`Enter value for ${variable}`}
                variant="outlined"
                required
                helperText={`This will replace {{${variable}}} in the template`}
              />
            </Grid>
          ))}
        </Grid>
      </Paper>
    );
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />

      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          Compose SMS
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Send SMS to selected recipients
        </Typography>
      </Box>

      {/* Bulk Send Alert */}
      {isBulkSend && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body1" gutterBottom>
            <strong>Bulk Send Mode:</strong> This SMS will be sent to all {selectedType}s matching your search criteria.
          </Typography>
          <Typography variant="body2" sx={{ fontSize: 16, textAlign: "start" }}>
            {getBulkSendDescription()}
          </Typography>
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Main Composition Area */}
        <Grid item xs={12} md={8}>
          {/* Recipients Summary */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recipients ({getRecipientSummary()})
            </Typography>

            {isBulkSend ? (
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: "start" }}>
                  SMS will be sent to all {selectedType}s matching your search criteria.
                  {Object.keys(searchFilters || {}).length > 0 && (
                    <Box sx={{ mt: 1, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                      <Typography variant="subtitle2" sx={{ textAlign: "start" }}>Applied Filters:</Typography>
                      {Object.entries(searchFilters || {}).map(([key, value]) => (
                        value && (
                          <Typography key={key} variant="body2" sx={{ textAlign: "start" }}>
                            {key}: {value}
                          </Typography>
                        )
                      ))}
                    </Box>
                  )}
                </Typography>
              </Box>
            ) : (
              <>
                {/* Recipients with phone numbers */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Recipients with phone numbers ({getRecipientsWithPhone().length}):
                  </Typography>
                  <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                    {getRecipientsWithPhone().slice(0, 10).map((user, index) => (
                      <Chip
                        key={index}
                        label={`${user.name} (${user.phone})`}
                        variant="outlined"
                        size="small"
                        color="success"
                      />
                    ))}
                    {getRecipientsWithPhone().length > 10 && (
                      <Chip
                        label={`+${getRecipientsWithPhone().length - 10} more`}
                        variant="outlined"
                        size="small"
                        color="primary"
                      />
                    )}
                  </Box>
                </Box>

                {/* Recipients without phone numbers */}
                {getRecipientsWithoutPhone().length > 0 && (
                  <Alert severity="warning" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      {getRecipientsWithoutPhone().length} recipient(s) don't have phone numbers and will be skipped:
                    </Typography>
                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 1 }}>
                      {getRecipientsWithoutPhone().slice(0, 5).map((user, index) => (
                        <Chip
                          key={index}
                          label={user.name}
                          variant="outlined"
                          size="small"
                          color="warning"
                        />
                      ))}
                      {getRecipientsWithoutPhone().length > 5 && (
                        <Chip
                          label={`+${getRecipientsWithoutPhone().length - 5} more`}
                          variant="outlined"
                          size="small"
                          color="warning"
                        />
                      )}
                    </Box>
                  </Alert>
                )}
              </>
            )}
          </Paper>

          {/* Template Selection */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              SMS Template (Required)
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Select a predefined template for your SMS message.
            </Typography>
            <TemplateSelector
              templates={templates}
              selectedTemplate={smsData.templateId}
              onTemplateSelect={handleTemplateSelect}
              loading={loading}
              type="sms"
            />
          </Paper>

          {/* Template Variables Input */}
          {renderVariableInputs()}

          {/* SMS Content Preview */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              SMS Content Preview
            </Typography>

            <TextField
              label="SMS Message Preview"
              fullWidth
              multiline
              rows={6}
              value={smsData.content}
              placeholder="Select a template above to see the preview..."
              helperText={`${getCharacterCount()}/1600 characters (${getSMSCount()} SMS${getSMSCount() > 1 ? ' messages' : ''})`}
              error={getCharacterCount() > 1600}
              disabled
              sx={{ mb: 2 }}
            />

            {getSMSCount() > 1 && (
              <Alert severity="info">
                This message will be sent as {getSMSCount()} SMS messages due to length.
              </Alert>
            )}

            {getCharacterCount() > 1600 && (
              <Alert severity="error" sx={{ mt: 1 }}>
                Message is too long. Please reduce the content to 1600 characters or less.
              </Alert>
            )}
          </Paper>

          {/* Action Buttons */}
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end", flexWrap: "wrap" }}>
              <Button
                variant="outlined"
                onClick={() => setShowPreview(true)}
                disabled={!smsData.content}
              >
                Preview
              </Button>

              {/* Regular Send Button (only show if not bulk send or has specific recipients) */}
              {!isBulkSend && selectedUsers.length > 0 && (
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSendSMS}
                  disabled={sending || !smsData.content || getRecipientsWithPhone().length === 0}
                  startIcon={sending && <CircularProgress size={20} />}
                >
                  {sending ? "Sending..." : `Send SMS to ${getRecipientsWithPhone().length} recipients`}
                </Button>
              )}

              {/* Bulk Send Button */}
              <Button
                variant="contained"
                color="secondary"
                onClick={() => setBulkConfirmDialog(true)}
                disabled={bulkSending || !smsData.content}
                startIcon={bulkSending && <CircularProgress size={20} />}
              >
                {bulkSending ? "Sending..." : `Bulk Send to All ${selectedType.charAt(0).toUpperCase() + selectedType.slice(1)}s`}
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, position: "sticky", top: 20 }}>
            <Typography variant="h6" gutterBottom>
              SMS Summary
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Recipients
              </Typography>
              <Typography variant="body1">
                {getRecipientSummary()}
              </Typography>
            </Box>

            {!isBulkSend && (
              <>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Valid Phone Numbers
                  </Typography>
                  <Typography variant="body1" color={getRecipientsWithPhone().length === 0 ? "error" : "inherit"}>
                    {getRecipientsWithPhone().length}
                  </Typography>
                </Box>
              </>
            )}

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Template
              </Typography>
              <Typography variant="body1">
                {smsData.useTemplate ? selectedTemplate?.name || "Using template" : "No template selected"}
              </Typography>
            </Box>

            {selectedTemplate && selectedTemplate.variables && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Variables
                </Typography>
                <Typography variant="body1">
                  {selectedTemplate.variables.length} variable{selectedTemplate.variables.length > 1 ? 's' : ''}
                </Typography>
              </Box>
            )}

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Character Count
              </Typography>
              <Typography variant="body1" color={getCharacterCount() > 1600 ? "error" : "inherit"}>
                {getCharacterCount()}/1600
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                SMS Count
              </Typography>
              <Typography variant="body1">
                {getSMSCount()} message{getSMSCount() > 1 ? 's' : ''}
              </Typography>
            </Box>

            {isBulkSend && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Send Mode
                </Typography>
                <Typography variant="body1" color="secondary">
                  Bulk Send (All {selectedType}s)
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Bulk Send Confirmation Dialog */}
      <Dialog
        open={bulkConfirmDialog}
        onClose={() => setBulkConfirmDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Confirm Bulk SMS Send
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to send this SMS to all {selectedType}s matching your search criteria?

            <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                SMS Details:
              </Typography>
              <Typography variant="body2">
                <strong>Content Length:</strong> {getCharacterCount()} characters ({getSMSCount()} SMS)
              </Typography>
              <Typography variant="body2">
                <strong>Recipients:</strong> All {selectedType}s
              </Typography>
              <Typography variant="body2">
                <strong>Template:</strong> {smsData.useTemplate ? selectedTemplate?.name || "Using template" : "No template"}
              </Typography>

              {Object.keys(searchFilters || {}).length > 0 && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="subtitle2">Applied Filters:</Typography>
                  {Object.entries(searchFilters || {}).map(([key, value]) => (
                    value && (
                      <Typography key={key} variant="body2">
                        {key}: {value}
                      </Typography>
                    )
                  ))}
                </Box>
              )}
            </Box>

            <Alert severity="warning" sx={{ mt: 2 }}>
              This action cannot be undone. The SMS will be sent to all matching recipients with valid phone numbers.
            </Alert>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkConfirmDialog(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleBulkSendSMS}
            variant="contained"
            color="secondary"
            disabled={bulkSending}
          >
            {bulkSending ? "Sending..." : "Confirm Bulk Send"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Preview Modal */}
      <MessagePreview
        open={showPreview}
        onClose={() => setShowPreview(false)}
        content={smsData.content}
        type="sms"
        recipients={isBulkSend ? [] : getRecipientsWithPhone()}
        templateVariables={templateVariables}
        isBulkPreview={isBulkSend}
      />
    </Container>
  );
};

export default SMSComposer;