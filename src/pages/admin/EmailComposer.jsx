import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
} from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../../components/common/BackButton";
import RichTextEditor from "../../components/admin/RichTextEditor";
import TemplateSelector from "../../components/admin/TemplateSelector";
import MessagePreview from "../../components/admin/MessagePreview";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { customEmailTemplate } from "../../utils/constant";

const EmailComposer = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = UseToast();

  const {
    selectedUsers = [],
    selectedType = "",
    mode = "email",
    isBulkSend = false,
    searchFilters = {},
  } = location.state || {};

  const [emailData, setEmailData] = useState({
    subject: "",
    content: "",
    // templateId: "",
    // useTemplate: false,
  });

  // const [templates, setTemplates] = useState([]);
  // const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [bulkSending, setBulkSending] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [bulkConfirmDialog, setBulkConfirmDialog] = useState(false);

  useEffect(() => {
    if (selectedUsers.length === 0 && !isBulkSend) {
      toast.error("No recipients selected");
      navigate("/dashboard/email");
      return;
    }
    // fetchTemplates();
  }, []);

  // const fetchTemplates = async () => {
  //   setLoading(true);
  //   try {
  //     const response = await Client.get("/admin/email-templates");
  //     if (response.data.success) {
  //       const groupedTemplates = response.data.data.templates || {};

  //       // Flatten the object into a single array
  //       const flatTemplates = Object.entries(groupedTemplates).flatMap(
  //         ([category, items]) =>
  //           items.map((template) => ({ ...template, category }))
  //       );

  //       setTemplates(flatTemplates); // now it's an array like TemplateSelector expects
  //     }
  //   } catch (error) {
  //     console.error("Error fetching templates:", error);
  //     toast.error("Failed to load email templates");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // const handleTemplateSelect = (template) => {
  //   if (template) {
  //     setEmailData((prev) => ({
  //       ...prev,
  //       subject: template.subject || "",
  //       content: template.content || "",
  //       templateId: template.id,
  //       useTemplate: true,
  //     }));
  //   } else {
  //     setEmailData((prev) => ({
  //       ...prev,
  //       subject: "",
  //       content: "",
  //       templateId: "",
  //       useTemplate: false,
  //     }));
  //   }
  // };

  const handleInputChange = (field, value) => {
    setEmailData((prev) => ({
      ...prev,
      [field]: value,
      // useTemplate:
        // field === "content" || field === "subject" ? false : prev.useTemplate,
    }));
  };

  const validateEmail = () => {
    if (!emailData.subject.trim()) {
      toast.error("Subject is required");
      return false;
    }
    if (!emailData.content.trim()) {
      toast.error("Email content is required");
      return false;
    }
    return true;
  };

  // Regular send email (with recipient data)
  const handleSendEmail = async () => {
    if (!validateEmail()) return;

    setSending(true);
    try {
      const payload = {
        recipients: selectedUsers.map((user) => ({
          id: user.id,
          email: user.email,
          name: user.name,
          type: user.type,
        })),
        subject: emailData.subject,
        content: emailData.content,
        // templateId: emailData.useTemplate ? emailData.templateId : null,
        recipientType: selectedType,
      };

      const response = await Client.post("/admin/send-email", payload);

      if (response.data.success) {
        toast.success(
          `Email sent successfully to ${selectedUsers.length} recipients`
        );
        navigate("/dashboard/email");
      } else {
        toast.error(response.data.message || "Failed to send email");
      }
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error("Failed to send email. Please try again.");
    } finally {
      setSending(false);
    }
  };

  // Bulk send email (without fetching all recipient data)
  const handleBulkSendEmail = async () => {
    if (!validateEmail()) return;

    setBulkSending(true);
    try {
      const payload = {
        subject: emailData.subject,
        content: emailData.content,
        // templateId: emailData.useTemplate ? emailData.templateId : null,
        recipientType: selectedType,
        searchFilters: searchFilters || {},
        bulkSend: true,
      };

      const response = await Client.post("/admin/send-bulk-email", payload);

      if (response.data.success) {
        const sentCount = response.data.sentCount || "all matching";
        toast.success(
          `Email sent successfully to ${sentCount} recipients`
        );
        navigate("/dashboard/email");
      } else {
        toast.error(response.data.message || "Failed to send bulk email");
      }
    } catch (error) {
      console.error("Error sending bulk email:", error);
      toast.error("Failed to send bulk email. Please try again.");
    } finally {
      setBulkSending(false);
      setBulkConfirmDialog(false);
    }
  };

  const getRecipientSummary = () => {
    if (isBulkSend) {
      const typeLabel =
        selectedType.charAt(0).toUpperCase() + selectedType.slice(1);
      return `All ${typeLabel}s <Bulk Send>`;
    }

    const typeLabel =
      selectedType.charAt(0).toUpperCase() + selectedType.slice(1);
    return `${selectedUsers.length} ${typeLabel}${
      selectedUsers.length > 1 ? "s" : ""
    }`;
  };

  const getBulkSendDescription = () => {
    const filters = Object.entries(searchFilters || {})
      .filter(([key, value]) => value && value !== "")
      .map(([key, value]) => `${key}: ${value}`)
      .join(", ");

    return filters ? `Filtered by: ${filters}` : "No filters applied - all users";
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />

      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          Compose Email
        </Typography>
        <Typography variant="subtitle1" color="black">
          Send email to selected recipients
        </Typography>
      </Box>

      {/* Bulk Send Alert */}
      {isBulkSend && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body1" gutterBottom>
            <strong>Bulk Send Mode:</strong> This email will be sent to all {selectedType}s matching your search criteria.
          </Typography>
          <Typography variant="body2" sx={{fontSize:20, textAlign:"start"}}>
            {getBulkSendDescription()}
          </Typography>
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Main Composition Area */}
        <Grid item xs={12} md={8}>
          {/* Recipients Summary */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recipients ({getRecipientSummary()})
            </Typography>

            {isBulkSend ? (
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{textAlign:"start"}}>
                  Email will be sent to all {selectedType}s matching your search criteria.
                  {Object.keys(searchFilters || {}).length > 0 && (
                    <Box sx={{ mt: 1, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                      <Typography variant="subtitle2" sx={{textAlign:"start"}}>Applied Filters:</Typography>
                      {Object.entries(searchFilters || {}).map(([key, value]) => (
                        value && (
                          <Typography key={key} variant="body2" sx={{textAlign:"start"}}>
                            {key}: {value}
                          </Typography>
                        )
                      ))}
                    </Box>
                  )}
                </Typography>
              </Box>
            ) : (
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                {selectedUsers.slice(0, 10).map((user, index) => (
                  <Chip
                    key={index}
                    label={`${user.name} (${user.email})`}
                    variant="outlined"
                    size="small"
                  />
                ))}
                {selectedUsers.length > 10 && (
                  <Chip
                    label={`+${selectedUsers.length - 10} more`}
                    variant="outlined"
                    size="small"
                    color="primary"
                  />
                )}
              </Box>
            )}
          </Paper>

          {/* Template Selection */}
          {/* <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Email Template (Optional)
            </Typography>
            <TemplateSelector
              templates={templates}
              selectedTemplate={emailData.templateId}
              onTemplateSelect={handleTemplateSelect}
              loading={loading}
              type="email"
            />
          </Paper> */}

          {/* Email Composition */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Email Content
            </Typography>

            <TextField
              fullWidth
              placeholder="Enter your email subject..."
              value={emailData.subject}
              onChange={(e) => handleInputChange("subject", e.target.value)}
              sx={{ mb: 3 }}
              required
            />

            <RichTextEditor
              value={`<p>Hello chessbrigade</p>`}
              onChange={(content) => handleInputChange("content", content)}
              placeholder="Write your email content here..."
              label="Email Body"
              minHeight={300}
            />
          </Paper>

          {/* Action Buttons */}
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end", flexWrap: "wrap" }}>
              <Button
                variant="outlined"
                onClick={() => setShowPreview(true)}
                disabled={!emailData.subject || !emailData.content}
              >
                Preview
              </Button>

              {/* Regular Send Button (only show if not bulk send or has specific recipients) */}
              {!isBulkSend && selectedUsers.length > 0 && (
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSendEmail}
                  disabled={sending || !emailData.subject || !emailData.content}
                  startIcon={sending && <CircularProgress size={20} />}
                >
                  {sending ? "Sending..." : "Send Email"}
                </Button>
              )}

              {/* Bulk Send Button */}
              <Button
                variant="contained"
                color="secondary"
                onClick={() => setBulkConfirmDialog(true)}
                disabled={bulkSending || !emailData.subject || !emailData.content}
                startIcon={bulkSending && <CircularProgress size={20} />}
              >
                {bulkSending ? "Sending..." : `Bulk Send to All ${selectedType.charAt(0).toUpperCase() + selectedType.slice(1)}s`}
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, position: "sticky", top: 20 }}>
            <Typography variant="h6" gutterBottom>
              Email Summary
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="black" sx={{ textAlign:"start"}}>
                Recipients
              </Typography>
              <Typography variant="body1">{getRecipientSummary()}</Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="black" sx={{ textAlign:"start"}}>
                Subject
              </Typography>
              <Typography variant="body1" sx={{ textAlign:"start"}}>
                {emailData.subject || "No subject"}
              </Typography>
            </Box>

            {/* <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="black" sx={{ textAlign:"start"}}>
                Template
              </Typography>
              <Typography variant="body1">
                {emailData.useTemplate ? "Using template" : "Custom content"}
              </Typography>
            </Box> */}

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="black" sx={{ textAlign:"start"}}>
                Content Length
              </Typography>
              <Typography variant="body1">
                {emailData.content.replace(/<[^>]*>/g, "").length} characters
              </Typography>
            </Box>

            {isBulkSend && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="black" sx={{ textAlign:"start"}}>
                  Send Mode
                </Typography>
                <Typography variant="body1" color="secondary">
                  Bulk Send (All {selectedType}s)
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Bulk Send Confirmation Dialog */}
      <Dialog
        open={bulkConfirmDialog}
        onClose={() => setBulkConfirmDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Confirm Bulk Email Send
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to send this email to all {selectedType}s matching your search criteria?

            <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Email Details:
              </Typography>
              <Typography variant="body2">
                <strong>Subject:</strong> {emailData.subject}
              </Typography>
              <Typography variant="body2">
                <strong>Recipients:</strong> All {selectedType}s
              </Typography>

              {Object.keys(searchFilters || {}).length > 0 && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="subtitle2">Applied Filters:</Typography>
                  {Object.entries(searchFilters || {}).map(([key, value]) => (
                    value && (
                      <Typography key={key} variant="body2">
                        {key}: {value}
                      </Typography>
                    )
                  ))}
                </Box>
              )}
            </Box>

            <Alert severity="warning" sx={{ mt: 2 }}>
              This action cannot be undone. The email will be sent to all matching recipients.
            </Alert>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkConfirmDialog(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleBulkSendEmail}
            variant="contained"
            color="secondary"
            disabled={bulkSending}
          >
            {bulkSending ? "Sending..." : "Confirm Bulk Send"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Preview Modal */}
      <MessagePreview
        open={showPreview}
        onClose={() => setShowPreview(false)}
        subject={emailData.subject}
        content={emailData.content}
        type="email"
        recipients={isBulkSend ? [] : selectedUsers}
        isBulkPreview={isBulkSend}
      />
    </Container>
  );
};

export default EmailComposer;
