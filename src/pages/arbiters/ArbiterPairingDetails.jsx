import { <PERSON>, Button, Container } from "@mui/material";
import React, { useEffect, useState } from "react";
import BackButton from "../../components/common/BackButton";
import { useParams } from "react-router-dom";
import { Client } from "../../api/client";

import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

import PairingDetailsUi from "../../components/common/PairingDetailsUi";
import UseToast from "../../lib/hooks/UseToast";
const DynamicPopup = React.lazy(() =>
  import("../../components/common/DynamicPopup")
);

const PairingDetails = () => {
  const { title: id } = useParams();
  const newTitle = encodeURIComponent(id);

  const [tournamentDetails, setTournamentDetails] = useState([]);

  const [loading, setLoading] = useState(false);
  const [currentRound, setCurrentRound] = useState(0);

  const [popupOpen, setPopupOpen] = useState(false);
  const toast = UseToast();

  const { user } = UseGlobalContext();
  const fetchCurrentRound = async () => {
    setLoading(true);
    try {
      const response = await Client.get(
        `/pairing-import/tournament/pairing/round`,
        { params: { id } }
      );

      if (response.data.success) {
        setCurrentRound(response.data.currentRound);
      } else {
        console.error(
          response.data.error.message || "something went wrong in current Round"
        );
      }
    } catch (error) {
      console.error("Error fetching current round:", error);

      // navigate("/tournaments");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const fetchTournamentDetails = async () => {
      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${newTitle}`);
        if (response.data.success) {
          setTournamentDetails(response.data.data);
        } else {
          console.error(
            response.data.message || "Failed to fetch tournament details"
          );
          // navigate("/tournaments");
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);

        // navigate("/tournaments");
      } finally {
        setLoading(false);
      }
    };
    fetchTournamentDetails();
    fetchCurrentRound();
  }, [newTitle]);
  const handleSendPairingNotification = async () => {
    try {
      if (!tournamentDetails && !currentRound) {
        toast.error("Please select round");
        return;
      }
      const response = await Client.post(`arbiter/pairing-notifications`, {
        tournamentId: id,
        round: currentRound,
      });
      if (response.data.success) {
        toast.success(response.data.message);
      } else {
        toast.error(response.data.message);
      }
    } catch (error) {
      console.error("Error fetching tournament details:", error);
      toast.error("An error occurred while fetching tournament details");
      // navigate("/tournaments");
    }
  };

  return (
    <Container maxWidth={"xl"} sx={{ py: 4, pt: 2, minHeight: "70dvh" }}>
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",

          alignItems: "center",
        }}
      >
        <BackButton />
        {user?.role === "arbiter" && (
          <Box sx={{ display: "flex", gap: 2 }}>
            <Button
              size="small"
              variant="contained"
              onClick={() => setPopupOpen(true)}
            >
              Upload
            </Button>
          </Box>
        )}
      </Box>
      <PairingDetailsUi
        tournamentDetails={tournamentDetails}
        loading={loading}
        setLoading={setLoading}
        currentRound={currentRound}
      />
      {user?.role === "arbiter" && (
        <Box sx={{ my: 2, display: "flex", justifyContent: "flex-end" }}>
          <Button
            size="small"
            variant="contained"
            onClick={handleSendPairingNotification}
          >
            Send Pairing Details
          </Button>
        </Box>
      )}

      <DynamicPopup
        open={popupOpen}
        onClose={() => setPopupOpen(false)}
        title="Upload Pairing Details"
        file_title="Pairing Details"
        tournament={tournamentDetails}
        type={0}
        // content={<UploadPairingDetails tournamentId={id} round={round}
      />
    </Container>
  );
};

export default PairingDetails;
