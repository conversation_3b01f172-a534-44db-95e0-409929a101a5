import React, { useEffect, useState } from "react";
import {
  Avatar,
  Box,
  Button,
  Container,
  Paper,
  Skeleton,
  Stack,
  Typography,
} from "@mui/material";
import { Person, Add } from "@mui/icons-material";
import { useNavigate, useParams } from "react-router-dom";
import UseToast from "../../lib/hooks/UseToast";
import { Client } from "../../api/client";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import { DetailTable } from "../../components/common/DetailTable";
import ClubInvitationModal from "../../components/club/ClubInvitationModal";
import BackButton from "../../components/common/BackButton";

const ArbiterDetailsPage = () => {
  const [arbiterInfo, setArbiterInfo] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [invitationModalOpen, setInvitationModalOpen] = useState(false);
  const toast = UseToast();
  const { id: cbid } = useParams();

  const navigate = useNavigate();
  // Fetch arbiter information
  useEffect(() => {
    const fetchArbiterInfo = async () => {
      setLoading(true);

      setError(null);
      try {
        const response = await Client.get(`/arbiter/single/${cbid}`);
        if (response.status === 204) {
          toast.info("Arbiter not found");
          navigate(-1);
          return;
        }
        if (!response.data.success) {
          toast.error(response.data.message);
          setError(response.data.message);
          return;
        }
        setArbiterInfo(response.data.data);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching arbiter info:", error);
        setError("Error fetching arbiter info");
        setLoading(false);
      }
    };

    fetchArbiterInfo();
  }, [navigate]);

  // Format arbiter data for display
  const formattedArbiterData = formatArbiterData(arbiterInfo);

  return (
    <Container maxWidth="xl" sx={{ py: 4,pt: 2 }}>
      <BackButton />
      <Paper
        elevation={3}
        sx={{
          bgcolor: "background.default",
          minHeight: "80vh",
          borderRadius: 2,
          overflow: "hidden",
          mb: 4,
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            flexDirection: { xs: "column", sm: "row" },
            flexFlow: "row",
            px: { xs: "4vw", sm: "10vw" },
            py: 2,
            borderBottom: "1px solid #f0f0f0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              alignSelf: "flex-start",
              gap: 2,
              width: "100%",
            }}
          >
            <Avatar
              src={arbiterInfo?.ArbiterDetail?.profileUrl}
              sx={{
                width: {
                  xs: 80,
                  sm: 80,
                  md: 100,
                  lg: 100,
                  xl: 100,
                },
                height: {
                  xs: 80,
                  sm: 80,
                  md: 100,
                  lg: 100,
                  xl: 100,
                },
                bgcolor: "#f5f5f5",
                border: "1px solid #00000030",
                color: "#000",
              }}
            >
              {!arbiterInfo?.ArbiterDetail?.profileUrl && (
                <Person sx={{ fontSize: { xs: 50, md: 60 } }} />
              )}
            </Avatar>
            <Stack sx={{ width: "100%", textAlign: "start" }}>
              {!loading ? (
                <>
                  <Typography variant="h4" component="h4" fontWeight="500">
                    {arbiterInfo?.name || "-"}
                  </Typography>
                  <Typography
                    variant="h6"
                    component="h6"
                    fontWeight="400"
                    sx={{ fontSize: { xs: "14px", sm: "16px" } }}
                  >
                    CBID: {arbiterInfo?.cbid || "-"}
                  </Typography>
                  <Typography
                    variant="h6"
                    component="h6"
                    fontWeight="400"
                    sx={{ fontSize: { xs: "14px", sm: "16px" } }}
                  >
                    FIDE Rating: {arbiterInfo?.ArbiterDetail?.fideRating || "-"}
                  </Typography>
                </>
              ) : (
                <Skeleton variant="text" width={200} />
              )}
            </Stack>
          </Box>
        </Box>

        {/* Arbiter Information */}
        <Box>
          {loading ? (
            // Loading skeleton
            Array(6)
              .fill(0)
              .map((_, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Skeleton variant="rectangular" height={40} />
                </Box>
              ))
          ) : error ? (
            // Error message
            <Typography color="error" variant="h6" align="center">
              {error}
            </Typography>
          ) : (
            // Arbiter details table
            <DetailTable
              details={formattedArbiterData.details}
              rowColor={{ odd: "#A5D8C626", even: "#BEDDF04F" }}
            />
          )}
        </Box>
      </Paper>

      {/* Club Invitation Modal */}
      <ClubInvitationModal
        open={invitationModalOpen}
        onClose={() => setInvitationModalOpen(false)}
        arbiterData={arbiterInfo}
      />
    </Container>
  );
};

export default ArbiterDetailsPage;

/**
 * Format arbiter data for display in the detail table
 * @param {Object} arbiter - Arbiter data from API
 * @returns {Object} Formatted arbiter data with title and details
 */
function formatArbiterData(arbiter) {
  if (!arbiter || !arbiter.ArbiterDetail) {
    return {
      title: "",
      details: [],
    };
  }

  // Create a list of details to display
  const details = [
    // {
    //   label: "Full Name",
    //   value: arbiter.name || "-",
    // },
    // {
    //   label: "CBID",
    //   value: arbiter.cbid || "-",
    // },
    {
      label: "Official ID",
      value: arbiter.ArbiterDetail.officialId || "-",
    },
    {
      label: "FIDE ID",
      value: arbiter.ArbiterDetail.fideId || "-",
    },
    {
      label: "ACIF ID",
      value: arbiter.ArbiterDetail.aicfId || "-",
    },
    {
      label: "State ID",
      value: arbiter.ArbiterDetail.stateId || "-",
    },
    {
      label: "District ID",
      value: arbiter.ArbiterDetail.districtId || "-",
    },

    {
      label: "Country",
      value: arbiter.ArbiterDetail.country || "-",
    },
    {
      label: "State",
      value: arbiter.ArbiterDetail.state || "-",
    },
    {
      label: "District",
      value: arbiter.ArbiterDetail.district || "-",
    },
  ];

  return {
    title: arbiter.name || "",
    details: details,
  };
}
