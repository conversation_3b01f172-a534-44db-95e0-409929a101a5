import React from "react";
import { Grid, Typography, Stack, Box } from "@mui/material";
import FormRadioField from "./FormRadioField";
import FormCheckboxGroup from "./FormCheckboxGroup";

/**
 * FormOtherDetails - A form component for other tournament details using React Hook Form
 *
 * @param {Object} props - Component props
 * @param {Object} props.control - React Hook Form control object
 */
const FormOtherDetails = ({ control }) => {
  // Food facility options
  const foodOptions = [
    { value: "breakfast", label: "Breakfast" },
    { value: "lunch", label: "Lunch" },
    { value: "dinner", label: "Dinner" },
    { value: "snacks", label: "Snacks" },
    { value: "beverages", label: "Beverages" },
  ];

  return (
    <>
      <Typography
        variant="h4"
        fontWeight="500"
        sx={{ mt: 6, mb: 3, textAlign: "start", fontWeight: "bold" }}
      >
        Others:
      </Typography>
      <Grid container spacing={4}>
        <Grid item xs={12} md={6}>
          <FormRadioField
            name="chessboardProvided"
            control={control}
            title="Chess Board Provided"
            options={[
              { value: true, label: "Yes" },
              { value: false, label: "No" },
            ]}
          />
        </Grid>

        <Grid item xs={12}>
          <FormRadioField
            name="timerProvided"
            control={control}
            title="Timer Provided"
            options={[
              { value: true, label: "Yes" },
              { value: false, label: "No" },
            ]}
          />
        </Grid>

        <Grid item xs={12}>
          <FormRadioField
            name="spotEntry"
            control={control}
            title="Spot Entry"
            options={[
              { value: true, label: "Yes" },
              { value: false, label: "No" },
            ]}
          />
        </Grid>

        <Grid item xs={12}>
          <FormRadioField
            name="parkingFacility"
            control={control}
            title="Parking Facility"
            options={[
              { value: "no", label: "Parking Not Available" },
              { value: "limited", label: "2 Wheeler Only" },
              { value: "yes", label: "2 & 4 Wheeler" },
            ]}
          />
        </Grid>

        <Grid item xs={12}>
          <FormCheckboxGroup
            name="foodFacility"
            control={control}
            title="Food Facility"
            options={foodOptions}
            direction={{ xs: "column", sm: "row" }}
            spacing={{ xs: 1, sm: 2 }}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default FormOtherDetails;
