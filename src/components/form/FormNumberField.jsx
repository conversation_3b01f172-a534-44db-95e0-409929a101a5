import React from "react";
import { Typography, TextField, Fade } from "@mui/material";
import { Controller } from "react-hook-form";
import { formatDecimal } from "../../utils/formatters";

// Custom Number Input component that works with React Hook Form
const FormNumberField = React.memo(
  ({
    name,
    control,
    placeholder,
    title,
    minLength = 1,
    maxLength = 20,
    sx = {},
    required = false,
    rules,
    ...rest
  }) => {
    return (
      <>
        <Typography
          variant="h6"
          sx={{
            textAlign: "start",
            p: "0px !important",
            m: "0px !important",
          }}
        >
          {title}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
        <Controller
          name={name}
          control={control}
          rules={rules}
          render={({ field, fieldState: { error } }) => {
            // Format the displayed value to remove decimal zeros
            const displayValue = field.value
              ? formatDecimal(field.value)
              : field.value;

            return (
              <TextField
                {...field}
                value={displayValue}
                fullWidth
                variant="outlined"
                margin="normal"
                placeholder={placeholder}
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  minLength: minLength,
                  maxLength: maxLength,
                }}
                onKeyPress={(e) => {
                  if (!/[0-9]/.test(e.key)) {
                    e.preventDefault();
                  }
                }}
                onChange={(e) => {
                  // Keep the original onChange behavior
                  field.onChange(e.target.value);
                }}
                sx={{
                  "& .MuiInputBase-input": { padding: "8px 14px" },
                  "& .MuiTextField-root": { mt: "0px !important" },
                  ...sx,
                }}
                error={!!error}
                helperText={
                  <Fade in={!!error}>
                    <Typography
                      component="span"
                      variant="caption"
                      color="error"
                    >
                      {error?.message}
                    </Typography>
                  </Fade>
                }
                {...rest}
              />
            );
          }}
        />
      </>
    );
  }
);

export default FormNumberField;
