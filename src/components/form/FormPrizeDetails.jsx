import React from "react";
import { Typography, Grid, Box, Stack } from "@mui/material";
import FormNumberField from "./FormNumberField";
import FormSelectField from "./FormSelectField";

const FormPrizeDetails = ({ control, setValue, watch }) => {
  return (
    <>
      <Typography
        variant="h4"
        fontWeight="500"
        sx={{ mb: 3, textAlign: "start", fontWeight: "bold" }}
      >
        Prize Details:
      </Typography>

      <Box sx={{ mb: 4 }}>
        <Grid
          container
          spacing={4}
          sx={{ ".MuiGrid-item": { pt: "8px !important" } }}
        >
          <Grid item xs={12} md={4}>
            <FormNumberField
              name="numberOfTrophiesMale"
              control={control}
              title="Number Of Trophies for Male"
              placeholder="Enter Number Of Trophies"
              required
              minLength={1}
              maxLength={4}
              rules={{
                required: "Number of trophies is required",
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormNumberField
              name="numberOfTrophiesFemale"
              control={control}
              title="Number Of Trophies for Female"
              placeholder="Enter Number Of Trophies"
              required
              minLength={1}
              maxLength={4}
              rules={{
                required: "Number of trophies is required",
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="h6" sx={{ textAlign: "start" }}>
              Prize Amount<span style={{ color: "red" }}>*</span>
            </Typography>
            <Stack
              sx={{
                display: "flex",
                flexDirection: "row",
                gap: 1,
                justifyContent: "center",
                alignItems: "flex-start",
              }}
            >
              <FormSelectField
                name="totalCashPrizeCurrency"
                control={control}
                options={[{ value: "INR", label: "INR" }]}
                sx={{ width: "100px", mt: "4px" }}
              />

              <FormNumberField
                name="totalCashPrizeAmount"
                control={control}
                placeholder="Enter Amount"
                minLength={1}
                maxLength={10}
                rules={{
                  required: "Total cash prize amount is required",
                }}
              />
            </Stack>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default FormPrizeDetails;
