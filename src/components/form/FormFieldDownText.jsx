import React, { useEffect, useState } from "react";
import {
  Text<PERSON><PERSON>,
  Chip,
  Box,
  Stack,
  Typography,
  FormHelperText,
} from "@mui/material";
import { Button } from "@mui/material";
import { Add } from "@mui/icons-material";
import { Controller } from "react-hook-form";

/**
 * FormFieldDownText - A component for adding multiple age categories with chips
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {Object} props.control - React Hook Form control object
 * @param {string} props.title - Label for the field
 * @param {boolean} props.required - Whether the field is required
 * @param {Object} props.rules - Additional validation rules
 * @param {Object} props.sx - Additional styles for the component
 */
const FormFieldDownText = ({
  name,
  control,
  title,
  required = false,
  setValue,
  rules = {},
  disabled = false,
  sx = {},
}) => {
  const [inputText, setInputText] = useState("");
  const [localError, setLocalError] = useState("");

  useEffect(() => {
    if (disabled) {
      setInputText("");
      setLocalError("");
      setValue([]);
    }
  }, [disabled]);

  // Combine required rule with other rules
  const validationRules = required
    ? {
        required: `${title} is required`,
        ...rules,
        validate: {
          validFormat: (value) => {
            if (!value || value.length === 0) return true;
            const allValid = value.every(
              (cat) =>
                cat.match(/^U([5-9]|[1-9][0-9]|1[0-4][0-9]|150)$/i) ||
                cat.toUpperCase() === "OPEN"
            );
            return allValid || "Invalid age category format";
          },
          ...rules.validate,
        },
      }
    : rules;

  return (
    <Box sx={{ mb: 2, ...sx }} key={name}>
      {title && (
        <Typography
          variant="h6"
          sx={{
            fontWeight: 400,
            fontSize: 20,
            textAlign: "start",
            color: "text.primary",
            userSelect: disabled ? "none" : "auto",
            opacity: disabled ? 0.5 : 1,
            "&::after": required
              ? {
                  content: '" *"',
                  color: "error.main",
                }
              : {},
          }}
        >
          {title}
        </Typography>
      )}

      <Controller
        name={name}
        control={control}
        rules={validationRules}
        disabled={disabled}
        render={({ field, fieldState: { error } }) => {
          const chipList = field.value || [];

          const handleInputChange = (event) => {
            setInputText(event.target.value);
          };

          const handleInputSubmit = (event) => {
            if (event.key === "Enter" || event.key === "/") {
              event.preventDefault();
              addChip();
            }
          };

          const handleButtonClick = (event) => {
            event.preventDefault();
            addChip();
          };

          const addChip = () => {
            const trimmedValue = inputText.trim().toUpperCase();

            // Validate input and add chip
            if (
              (trimmedValue.match(/^U([5-9]|[1-9][0-9]|1[0-4][0-9]|150)$/) ||
                trimmedValue === "OPEN") &&
              !chipList.includes(trimmedValue)
            ) {
              const newChipList = [...chipList, trimmedValue];
              field.onChange(newChipList);

              setInputText(""); // Clear input field after adding
              setLocalError(""); // Clear local error after valid input
            } else {
              setLocalError(
                "Invalid value. Please enter a valid age category (e.g., U8, U10, U12, U15, U18, U20, Open)"
              );
            }
          };

          const handleDelete = (chipToDelete) => {
            const newChipList = chipList.filter(
              (chip) => chip !== chipToDelete
            );
            field.onChange(newChipList);
          };

          return (
            <>
              <Stack
                direction="row"
                spacing={2}
                sx={{
                  justifyContent: "space-between",
                  alignItems: "flex-start",
                }}
              >
                <TextField
                  fullWidth
                  value={inputText}
                  disabled={disabled}
                  sx={{
                    mt: 0.5,
                    "& .MuiFormControl-root": { m: "0px !important" },
                  }}
                  onChange={handleInputChange}
                  onKeyPress={handleInputSubmit}
                  placeholder="U8 / U10 / U12 / U15 / U18 / U20 / Open"
                  error={!!localError || !!error}
                  helperText={localError}
                  size="small"
                />
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: "#2c2891",
                    fontSize: 16,
                    mt: "4px !important",
                  }}
                  disabled={disabled}
                  startIcon={<Add sx={{ color: "white" }} />}
                  onClick={handleButtonClick}
                >
                  Add
                </Button>
              </Stack>

              {error && !localError && (
                <FormHelperText error sx={{ ml: 1.5 }}>
                  {error.message}
                </FormHelperText>
              )}

              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 2 }}>
                {chipList.map((chip, index) => (
                  <Chip
                    key={index}
                    label={chip}
                    sx={{ bgcolor: "lightgreen" }}
                    onDelete={() => handleDelete(chip)}
                  />
                ))}
              </Box>
            </>
          );
        }}
      />
    </Box>
  );
};

export default FormFieldDownText;
