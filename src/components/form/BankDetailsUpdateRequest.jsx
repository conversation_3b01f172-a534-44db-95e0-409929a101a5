import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Grid,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import UseToast from "../../lib/hooks/UseToast";
import { Client } from "../../api/client";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

// Schema for the bank details update request form
const updateRequestSchema = z.object({
  reason: z
    .string()
    .min(10, "Reason must be at least 10 characters")
    .max(500, "Reason must not exceed 500 characters"),
  additionalInfo: z
    .string()
    .max(1000, "Additional information must not exceed 1000 characters")
    .optional()
    .nullable(),
});

const BankDetailsUpdateRequest = ({ open, onClose }) => {
  const toast = UseToast();
  const { user } = UseGlobalContext();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(updateRequestSchema),
    defaultValues: {
      reason: "",
      additionalInfo: "",
    },
  });

  const onSubmit = async (data) => {
    try {
      // Send the update request to the API
      const response = await Client.post(
        "/club/profile/bankdetails/update-request",
        { ...data, userId: user.id }
      );

      if (response.data.success) {
        toast.success(
          "Your bank details update request has been submitted successfully. The admin will review your request."
        );
        reset();
        onClose();
      } else {
        toast.error(response.data.message || "Failed to submit update request");
      }
    } catch (error) {
      console.error("Error submitting bank details update request:", error);
      toast.error(
        error.response?.data?.message ||
          "An error occurred while submitting your request"
      );
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: "1px solid #eee",
          pb: 2,
        }}
      >
        <Typography variant="h5" component="div" sx={{ fontWeight: 500 }}>
          Request Bank Details Update
        </Typography>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        <Box component="form" onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="body1" sx={{ mb: 1 }}>
                Bank details can only be updated with admin approval. Please
                provide a reason for your update request.
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="reason"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Reason for Update"
                    fullWidth
                    multiline
                    rows={4}
                    sx={{
                      "& .MuiFormLabel-root.MuiInputLabel-root": {
                        color: "black",
                      },
                    }}
                    error={!!errors.reason}
                    helperText={errors.reason?.message}
                    placeholder="Please explain why you need to update your bank details"
                    required
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="additionalInfo"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Additional Information (Optional)"
                    fullWidth
                    multiline
                    sx={{
                      "& .MuiFormLabel-root.MuiInputLabel-root": {
                        color: "black",
                      },
                    }}
                    rows={3}
                    error={!!errors.additionalInfo}
                    helperText={errors.additionalInfo?.message}
                    placeholder="Any additional information that might help with your request"
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, borderTop: "1px solid #eee" }}>
        <Button
          onClick={handleClose}
          variant="outlined"
          sx={{ mr: 1, fontSize: 16 }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit(onSubmit)}
          variant="contained"
          disabled={isSubmitting}
          sx={{
            bgcolor: "hsla(120, 49%, 35%, 1)",
            fontSize: 16,
            "&:hover": {
              bgcolor: "rgb(39, 104, 39)",
            },
          }}
        >
          Submit Request
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BankDetailsUpdateRequest;
