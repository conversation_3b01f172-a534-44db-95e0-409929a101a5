import React from "react";
import { Typography, Select, MenuItem, FormControl, Fade } from "@mui/material";
import { Controller } from "react-hook-form";
// Custom Select component that works with React Hook Form
const FormSelectField = React.memo(
  ({
    name,
    control,
    options,
    placeholder,
    title,
    required = false,
    rules,
    ...rest
  }) => {
    return (
      <>
        <Typography
          variant="h6"
          sx={{ textAlign: "start", p: "0px !important",pb:"4px !important"}}
        >
          {title} {required && <span style={{ color: "red" }}>*</span>}
        </Typography>{" "}
        <Controller
          name={name}
          control={control}
          rules={rules}
          render={({ field, fieldState: { error } }) => (
            <FormControl
              fullWidth
              error={!!error}
              sx={{
                minHeight: 70,
                "& .MuiSelect-select": { padding: "8px 14px" },
                "& .MuiFormControl-root": { mt: "0px !important" },
                ...rest?.sx,
              }}
            >
              <Select
                {...field}
                displayEmpty
                renderValue={(selected) => selected || placeholder}
                {...rest}
                sx={{textTransform:"capitalize"}}
              >
                {options.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
              {error && (
                <Fade in={!!error}>
                  <Typography component="span" variant="caption" color="error">
                    {error?.message}
                  </Typography>
                </Fade>
              )}
            </FormControl>
          )}
        />
      </>
    );
  }
);

export default FormSelectField;
