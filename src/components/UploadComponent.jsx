import React, { useState } from "react";
import { Box, Avatar, Typography, Grid, Button, IconButton } from "@mui/material";
import { CloudUpload, InsertDriveFile, Close } from "@mui/icons-material";
import compressImage from "../utils/imageCompressor";

const UploadComponent = ({
  setValue,
  watch,
  fileName,
  filePreview,
  errors,
  title = "Upload Brochure",
  size = 5,
}) => {
  const file = watch(fileName);
  const preview = watch(filePreview);

  const [uploadError, setUploadError] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileChange = async (e) => {
    const selectedFile = e.target.files[0];
    setUploadError(null);

    if (!selectedFile) return;

    const isImage = selectedFile?.type?.startsWith("image/");
    const isPdf = selectedFile.type === "application/pdf";

    if (!isImage && !isPdf) {
      setUploadError("Only images (JPG, PNG, WebP) or PDF files are allowed.");
      return;
    }

    if (selectedFile.size > size * 1024 * 1024) {
      setUploadError(`File must be smaller than ${size}MB.`);
      return;
    }

    setIsProcessing(true);

    try {
      let finalFile = selectedFile;

      if (isImage) {
        finalFile = await compressImage(selectedFile, 1000); // Compress image
      }

      setValue(fileName, finalFile);

      if (isImage) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setValue(filePreview, e.target.result);
        };
        reader.readAsDataURL(finalFile);
      } else {
        setValue(filePreview, null); // no preview for PDF
      }
    } catch (err) {
      console.error("File processing error:", err);
      setUploadError("Error processing the file.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRemoveFile = () => {
    setValue(fileName, null);
    setValue(filePreview, null);
    setUploadError(null);
  };

  return (
    <Grid item xs={12} sx={{ mt: 2 }}>
      <Typography variant="h6" sx={{ textAlign: "start" }}>
        {title}
      </Typography>

      <Button
        variant="outlined"
        size="small"
        component="label"
        endIcon={<CloudUpload />}
        sx={{
          mt: 2,
          textTransform: "none",
          width: "100%",
          px: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-start",
          py: 1,
        }}
        disabled={isProcessing}
      >
        <Typography variant="h6" sx={{ fontSize: 16 }}>
          {isProcessing ? "Uploading..." : `Upload Image/PDF (max ${size}MB)`}
        </Typography>
        <input
          type="file"
          hidden
          accept="image/jpeg,image/png,image/jpg,image/webp,application/pdf"
          onChange={handleFileChange}
        />
      </Button>

      {(errors?.[fileName]?.message || uploadError) && (
        <Typography
          color="error"
          variant="caption"
          sx={{ display: "block", mt: 1 }}
        >
          {errors?.[fileName]?.message || uploadError}
        </Typography>
      )}

      {file && (
        <Box sx={{ mt: 2, display: "flex", alignItems: "center", gap: 2 }}>
          {file?.type?.startsWith("image/") && preview ? (
            <Avatar
              src={preview}
              alt="Preview"
              sx={{ width: 100, height: 100, borderRadius: 1 }}
            />
          ) : (
            <InsertDriveFile color="action" sx={{ fontSize: 20 }} />
          )}

          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontSize: 16 }}>
              {file.name}
            </Typography>
          </Box>

          <IconButton
            onClick={handleRemoveFile}
            color="error"
            size="small"
            sx={{ ml: 1 }}
            aria-label="Remove file"
          >
            <Close />
          </IconButton>
        </Box>
      )}
    </Grid>
  );
};

export default UploadComponent;