import { Box, CircularProgress, Typography } from "@mui/material";
import { GoogleMap, useJs<PERSON>pi<PERSON>oa<PERSON>, <PERSON><PERSON> } from "@react-google-maps/api";
import { useState, useEffect, useCallback } from "react";
import { useController } from "react-hook-form";

// Define the API key at module level to avoid re-renders
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_MAPS_API_KEY;

// Define map container style outside component to prevent re-renders
const mapContainerStyle = {
  width: "100%",
  height: "200px",
  borderRadius: "8px",
};

// Define center of Tamil Nadu as default
const DEFAULT_CENTER = { lat: 20.5937, lng: 78.9629 };

const MapComponent = ({ control, name }) => {
  // Use useJsApiLoader instead of LoadScript for better performance
  const { isLoaded, loadError } = useJsApiLoader({
    googleMapsApiKey: GOOGLE_MAPS_API_KEY,
    // Add this to prevent multiple loads
    id: "google-map-script",
  });

  const [selectedLocation, setSelectedLocation] = useState(null);
  const [map, setMap] = useState(null);

  const {
    field,
    fieldState: { error: fieldError },
  } = useController({
    name,
    control,
    defaultValue: `https://www.google.com/maps/search/?api=1&query=${DEFAULT_CENTER.lat},${DEFAULT_CENTER.lng}`,
  });

  const getLatLngFromUrl = useCallback((url) => {
    try {
      const queryParam = new URL(url).searchParams.get("query");
      if (!queryParam) return null;
      const [lat, lng] = queryParam.split(",").map(Number);
      return { lat, lng };
    } catch (err) {
      console.error("Invalid URL:", err);
      return null;
    }
  }, []);

  // Initialize map reference
  const onLoad = useCallback((map) => {
    setMap(map);
  }, []);

  // Clear map reference on unmount
  const onUnmount = useCallback(() => {
    setMap(null);
  }, []);

  const handleMapClick = useCallback(
    (event) => {
      const location = {
        lat: event.latLng.lat(),
        lng: event.latLng.lng(),
      };
      setSelectedLocation(location);
      const newLocation = `https://www.google.com/maps/search/?api=1&query=${location.lat},${location.lng}`;
      field.onChange(newLocation);
    },
    [field]
  );

  // Parse coordinates from field value
  useEffect(() => {
    if (field.value) {
      const coordinates = getLatLngFromUrl(field.value);
      if (coordinates) {
        setSelectedLocation(coordinates);
      }
    }
  }, [field.value, getLatLngFromUrl]);

  // Render loading state
  if (!isLoaded) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height={200}
        bgcolor="grey.200"
        borderRadius={2}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (loadError) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height={200}
        bgcolor="grey.200"
        borderRadius={2}
      >
        <Typography color="error">
          Failed to load Google Maps: {loadError.message}
        </Typography>
      </Box>
    );
  }

  return (
    <Box position="relative">
      <GoogleMap
        mapContainerStyle={mapContainerStyle}
        center={selectedLocation || DEFAULT_CENTER}
        zoom={10}
        onClick={handleMapClick}
        onLoad={onLoad}
        onUnmount={onUnmount}
        options={{
          fullscreenControl: false, // Disable fullscreen control to prevent issues
        }}
      >
        {selectedLocation && <Marker position={selectedLocation} />}
      </GoogleMap>
      {fieldError && (
        <Typography color="error">{fieldError.message}</Typography>
      )}
    </Box>
  );
};

export default MapComponent;
