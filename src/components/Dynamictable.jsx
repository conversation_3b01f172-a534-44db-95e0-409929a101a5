import React, { useState } from "react";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Menu, MenuItem } from "@mui/material";
import "@mui/material/styles";
import FilterListIcon from '@mui/icons-material/FilterList';

const DynamicTable = ({ columns, rows }) => {
  const [filters, setFilters] = useState({});
  const [anchorEl, setAnchorEl] = useState(null);
  const [currentColumn, setCurrentColumn] = useState(null);

  const handleFilterClick = (event, column) => {
    setAnchorEl(event.currentTarget);
    setCurrentColumn(column);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setCurrentColumn(null);
  };

  const handleFilterChange = (column, value) => {
    setFilters(prevFilters => ({ ...prevFilters, [column]: value }));
  };

  const filteredRows = rows.filter(row => {
    return Object.keys(filters).every(column => {
      return row[column].toString().toLowerCase().includes(filters[column].toLowerCase());
    });
  });

  return (
    <TableContainer component={Paper} sx={{ boxShadow: 3, borderRadius: 2, overflow: "hidden" }}>
      <Table sx={{ minWidth: 650 }}>
        <TableHead>
          <TableRow sx={{ backgroundColor: "#CCBEF033" }}>
            {columns.map((column, index) => (
              <TableCell key={index} sx={{ fontWeight: "bold", textAlign: "left", padding: "12px 16px" }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  {column}
                  <IconButton onClick={(event) => handleFilterClick(event, column)} size="small">
                    <FilterListIcon />
                  </IconButton>
                  <Menu
                    anchorEl={anchorEl}
                    open={Boolean(anchorEl) && currentColumn === column}
                    onClose={handleClose}
                  >
                    {Array.from(new Set(rows.map(row => row[column]))).map((value, idx) => (
                      <MenuItem key={idx} onClick={() => handleFilterChange(column, value)}>
                        <input type="checkbox" /> {value}
                      </MenuItem>
                    ))}
                  </Menu>
                </div>
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        
        <TableBody>
          {filteredRows.map((row, rowIndex) => (
            <TableRow key={rowIndex} sx={{ "&:nth-of-type(odd)": { backgroundColor: "#BEDDF026" }, "&:nth-of-type(even)": { backgroundColor: "#DAECF81F" } }}>
              {columns.map((column, colIndex) => (
                <TableCell key={colIndex} sx={{ textAlign: "left", padding: "12px 16px" }}>
                  {row[column]}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default DynamicTable;