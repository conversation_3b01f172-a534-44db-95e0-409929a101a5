import {
  Box,
  Container,
  Grid,
  Stack,
  Typography,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import React from "react";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import { Link, useLocation } from "react-router-dom";
import FacebookIcon from "@mui/icons-material/Facebook";
import TwitterIcon from "@mui/icons-material/Twitter";
import InstagramIcon from "@mui/icons-material/Instagram";
import YouTubeIcon from "@mui/icons-material/YouTube";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

const FooterSection = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { isLoggedIn } = UseGlobalContext();
  const location = useLocation();

  const footerLinks = {
    left: [
      { title: "About Us", link: "/about-us" },
      { title: "Contact Us", link: "/contact-us" },
      // { title: "News & Articles", link: "#" },
    ],

    right: [
      { title: "Privacy Policy", link: "/policy/privacy-policy" },
      { title: "Fair Play Policy", link: "/policy/fair-play-policy" },
      { title: "Terms & Conditions", link: "/policy/terms-and-conditions" },
      { title: "Refund Policy", link: "/policy/refund-policy" },
    ],
  };

  const socialIcons = [
    {
      icon: (
        <FacebookIcon
          fontSize="large"
          sx={{ ":hover": { color: "#1877F2" } }}
        />
      ),
      link: "#",
    },
    {
      icon: (
        <TwitterIcon fontSize="large" sx={{ ":hover": { color: "#1DA1F2" } }} />
      ),
      link: "#",
    },
    {
      icon: (
        <InstagramIcon
          fontSize="large"
          sx={{ ":hover": { color: "#E4405F" } }}
        />
      ),
      link: "#",
    },
    {
      icon: (
        <YouTubeIcon fontSize="large" sx={{ ":hover": { color: "#FF0000" } }} />
      ),
      link: "#",
    },
  ];

  return (
    <Box
      sx={{
        bgcolor: "black",
        color: "white",
        pt: 4,
        px: { xs: 2, sm: 4, md: 6, lg: 8 },
      }}
    >
      <Container maxWidth="xl">
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Typography
              variant="h3"
              component={Link}
              to={isLoggedIn ? "/dashboard" : "/"}
              gutterBottom
              sx={{
                mr: 2,
                display: "flex",
                fontFamily: "'Prosto One', cursive",
                fontWeight: 400,
                letterSpacing: ".3rem",
                color: "inherit",
                textDecoration: "none",
                fontSize: { xs: "1.2rem", md: "1.3rem", lg: "1.8rem" },
              }}
            >
              ChessBrigade.com
            </Typography>
            <Typography
              variant="h5"
              sx={{ fontSize: { xs: "14px", md: "16px" } }}
            >
              Play Tournaments / Solve Puzzles
              <br />
              Practice Online &amp; Many more...
            </Typography>
          </Grid>

          <Grid item xs={12} md={4} maxWidth={"350px"}>
            <Typography
              variant="h6"
              sx={{ textAlign: "center", maxWidth: "350px" }}
              gutterBottom
            >
              Quick Links
            </Typography>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                gap: 2,
                maxWidth: "350px",

                flexWrap: "wrap",
              }}
            >
              <Stack alignItems="flex-start">
                {footerLinks.left.map((link, index) => (
                  <Link
                    key={index}
                    to={link.link}
                    style={{ textDecoration: "none", color: "inherit" }}
                  >
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <PlayArrowIcon
                        fontSize="small"
                        sx={{ color: "lightgreen" }}
                      />
                      <Typography
                        variant="h6"
                        sx={{
                          color:
                            location.pathname === link.link && "lightgreen",
                          ":hover": { color: "lightgreen" },
                          fontSize: { xs: "14px", md: "16px" },
                        }}
                      >
                        {link.title}
                      </Typography>
                    </Stack>
                  </Link>
                ))}
              </Stack>
              <Stack alignItems="flex-start">
                {footerLinks.right.map((link, index) => (
                  <Link
                    key={index}
                    to={link.link}
                    style={{ textDecoration: "none", color: "inherit" }}
                  >
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <PlayArrowIcon
                        fontSize="small"
                        sx={{ color: "lightgreen" }}
                      />
                      <Typography
                        variant="h6"
                        sx={{
                          color:
                            location.pathname === link.link && "lightgreen",
                          ":hover": { color: "lightgreen" },
                          fontSize: { xs: "14px", md: "16px" },
                        }}
                      >
                        {link.title}
                      </Typography>
                    </Stack>
                  </Link>
                ))}
              </Stack>
            </Box>
          </Grid>

          {/* <Grid item xs={12} md={4}>
            <Typography
              variant="h6"
              gutterBottom
              align={isMobile ? "left" : "center"}
            >
              Follow us
            </Typography>
            <Stack
              direction="row"
              spacing={2}
              justifyContent={isMobile ? "flex-start" : "center"}
            >
              {socialIcons.map((social, index) => (
                <Link key={index} to={social.link} color="inherit">
                  {social.icon}
                </Link>
              ))}
            </Stack>
          </Grid> */}
        </Grid>
        <Box width={"100%"} py={0.5}>
          <Typography
            variant="h6"
            align="center"
            sx={{
              mt: 2,
              opacity: 0.7,
              textAlign: "start",
              fontSize: { xs: "12px", md: "16px" },
            }}
          >
            © {new Date().getFullYear()} ChessBrigade. All rights reserved by
            JAI International. Powered By{" "}
            <a href="https://www.yalabs.tech" target="_blank">
              Yalabs
            </a>{" "}
            .
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default FooterSection;
