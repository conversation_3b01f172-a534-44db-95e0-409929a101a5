import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import {
  Box,
  Button,
  IconButton,
  Link,
  Modal,
  Stack,
  TextField,
  Typography,
  CircularProgress,
  FormHelperText,
} from "@mui/material";
import { useState, useEffect } from "react";
import { z } from "zod";
import backgroundImage from "../../assets/loginbackground.png";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

// Zod schema for email validation
const emailSchema = z.object({
  email: z.string().email({ message: "Invalid email format" }),
});

// Zod schema for OTP validation
const otpSchema = z.object({
  otp: z
    .string()
    .min(4, { message: "OTP is required" })
    .max(6, { message: "OTP must not exceed 6 digits" }),
});

// Zod schema for password validation with strong password requirements
const passwordSchema = z
  .object({
    newPassword: z
      .string()
      .min(8, { message: "Password must be at least 8 characters long" })
      .regex(/[A-Z]/, {
        message: "Password must contain at least one uppercase letter",
      })
      .regex(/[a-z]/, {
        message: "Password must contain at least one lowercase letter",
      })
      .regex(/[0-9]/, { message: "Password must contain at least one number" })
      .regex(/[^A-Za-z0-9]/, {
        message: "Password must contain at least one special character",
      }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

const ForgotPassword = ({ open, setOpen }) => {
  const toast = UseToast();
  const [formData, setFormData] = useState({
    email: "",
    otp: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formStep, setFormStep] = useState(1);
  const [sendingOtp, setSendingOtp] = useState(false);
  const [timer, setTimer] = useState(60);
  const [isResendDisabled, setIsResendDisabled] = useState(true);

  // Reset form when modal is opened/closed
  useEffect(() => {
    if (open) {
      setFormData({
        email: "",
        otp: "",
        newPassword: "",
        confirmPassword: "",
      });
      setErrors({});
      setFormStep(1);
    }
  }, [open]);

  // Timer for OTP resend
  useEffect(() => {
    let interval;
    if (isResendDisabled && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
    } else if (timer === 0) {
      setIsResendDisabled(false);
      clearInterval(interval);
    }

    return () => clearInterval(interval);
  }, [timer, isResendDisabled]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    // Clear error for the field being edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Validate email step
  const validateEmail = () => {
    try {
      emailSchema.parse({ email: formData.email });
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors = {};
        error.errors.forEach((err) => {
          const path = err.path[0].toString();
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
        toast.error(error.errors[0].message);
      }
      return false;
    }
  };

  // Validate OTP step
  const validateOtp = () => {
    try {
      otpSchema.parse({ otp: formData.otp });
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors = {};
        error.errors.forEach((err) => {
          const path = err.path[0].toString();
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
        toast.error(error.errors[0].message);
      }
      return false;
    }
  };

  // Validate password step
  const validatePassword = () => {
    try {
      passwordSchema.parse({
        newPassword: formData.newPassword,
        confirmPassword: formData.confirmPassword,
      });
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors = {};
        error.errors.forEach((err) => {
          const path = err.path[0].toString();
          newErrors[path] = err.message;
        });
        setErrors(newErrors);
        toast.error(error.errors[0].message);
      }
      return false;
    }
  };

  // Send OTP to email
  const handleSendOtp = async () => {
    if (!validateEmail()) {
      return;
    }

    setSendingOtp(true);
    try {
      setTimer(60);
      setIsResendDisabled(true);

      // Call the API endpoint to send OTP
      const response = await Client.post("/auth/send-email-otp", {
        email: formData.email,
        type: "reset-password",
      });

      if (!response.data.success) {
        toast.error(response.data.message || "Failed to send OTP");
        return;
      }

      toast.success("OTP sent successfully to your email");
      setFormStep(2); // Move to OTP verification step
    } catch (error) {
      console.error("Error sending OTP:", error);
      if (error.response?.status === 409) {
        toast.error(error.response.data.message || "User already exists");
      } else if (error.response?.status === 422) {
        toast.error(error.response.data.message || "Invalid input data");
      } else if (error.response?.status === 404) {
        toast.error("Email not found. Please check your email address.");
      } else {
        toast.error(
          error.response?.data?.message ||
            "Failed to send OTP. Please try again."
        );
      }
    } finally {
      setSendingOtp(false);
    }
  };

  // Reset password with OTP
  const handleResetPassword = async () => {
    if (!validateOtp() || !validatePassword()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const { email, otp, newPassword } = formData;

      const response = await Client.put("/auth/forgotpassword", {
        email,
        otp,
        newPassword,
      });

      if (!response.data.success) {
        toast.error(response.data.error || "Password reset failed");
        throw new Error(response.data.error || "Password reset failed");
      }

      toast.success("Password reset successfully");
      setOpen({ ...open, forgotpassword: false, login: true });

      setFormData({
        email: "",
        otp: "",
        newPassword: "",
        confirmPassword: "",
      });
      setFormStep(1);
    } catch (error) {
      console.error("Error resetting password:", error);
      if (error.response?.status === 400) {
        toast.error(error.response.data.message || "Invalid OTP");
      } else if (error.response?.status === 404) {
        toast.error(error.response.data.message || "OTP not found or expired");
      } else if (error.response) {
        toast.error(error.response.data.error || "Password reset failed");
      } else if (error.request) {
        toast.error("No response from server. Please try again later.");
      } else {
        toast.error("An error occurred. Please try again later.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submit based on current step
  const handleSubmit = async (e) => {
    if (e) e.preventDefault();

    if (formStep === 1) {
      handleSendOtp();
    } else if (formStep === 2) {
      handleResetPassword();
    }
  };

  // Password strength indicator
  const getPasswordStrength = (password) => {
    if (!password) return 0;

    let strength = 0;
    if (password.length >= 8) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[a-z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;

    return strength;
  };

  const getStrengthColor = (strength) => {
    if (strength <= 2) return "error.main";
    if (strength <= 3) return "warning.main";
    if (strength <= 4) return "info.main";
    return "success.main";
  };

  const getStrengthLabel = (strength) => {
    if (strength <= 1) return "Very Weak";
    if (strength <= 2) return "Weak";
    if (strength <= 3) return "Medium";
    if (strength <= 4) return "Strong";
    return "Very Strong";
  };

  const passwordStrength = getPasswordStrength(formData.newPassword);

  return (
    <Modal
      open={open}
      onClose={() => setOpen({ ...open, forgotpassword: false })}
      aria-labelledby="reset-password-modal"
      aria-describedby="reset-password-form"
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: "100%",
          bgcolor: "rgba(0, 0, 0, 0.5)",
          backdropFilter: "blur(2px)",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          paddingX: "3vw",
        }}
      >
        <Box
          sx={{
            width: 488,
            height: "auto",
            position: "relative",
            overflow: "hidden",
            borderRadius: 2,
            bgcolor: "black",
          }}
        >
          <Box
            sx={{
              width: "100%",
              height: "100%",
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: "cover",
              position: "relative",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              padding: 3,
            }}
          >
            <IconButton
              sx={{
                position: "absolute",
                top: 13,
                right: 21,
                color: "white",
              }}
              onClick={() => setOpen({ ...open, forgotpassword: false })}
            >
              <CloseIcon fontSize="large" />
            </IconButton>

            <Typography
              variant="h4"
              sx={{
                fontFamily: "Prosto One, Helvetica",
                color: "white",
                fontSize: 28,
                mb: 2,
              }}
            >
              ChessBrigade.com
            </Typography>

            <Typography
              variant="h4"
              sx={{
                fontFamily: "Poppins, Helvetica",
                color: "white",
                fontSize: 32,
                fontWeight: "bold",
                textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
                mb: 2,
              }}
            >
              Reset Password
            </Typography>

            <form onSubmit={handleSubmit}>
              <Stack spacing={2} sx={{ width: "100%", maxWidth: 336 }}>
                {formStep === 1 && (
                  <>
                    <Typography
                      sx={{
                        fontFamily: "Poppins, Helvetica",
                        color: "white",
                        fontSize: 16,
                        mb: 1,
                      }}
                    >
                      Enter your email address to receive an OTP
                    </Typography>

                    <TextField
                      name="email"
                      placeholder="Enter Email Address*"
                      value={formData.email}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          email: e.target.value,
                        });

                        // Clear error
                        if (errors.email) {
                          setErrors((prev) => {
                            const newErrors = { ...prev };
                            delete newErrors.email;
                            return newErrors;
                          });
                        }
                      }}
                      fullWidth
                      variant="outlined"
                      error={!!errors.email}
                      helperText={errors.email}
                      sx={{
                        bgcolor: "white",
                        borderRadius: 1,
                        "& .MuiOutlinedInput-root": {
                          height: 48,
                        },
                      }}
                      type="email"
                      autoComplete="email"
                    />

                    <Button
                      type="button"
                      variant="contained"
                      fullWidth
                      onClick={handleSendOtp}
                      disabled={sendingOtp || !formData.email}
                      sx={{
                        bgcolor: "#fff",
                        color: "black",
                        height: 48,
                        borderRadius: 1,
                        textTransform: "none",
                        fontFamily: "Poppins, Helvetica",
                        fontWeight: 500,
                        fontSize: 16,
                        mt: 1,
                        "&:hover": {
                          bgcolor: "#f0f0f0",
                        },
                      }}
                    >
                      {sendingOtp ? (
                        <CircularProgress size={24} color="inherit" />
                      ) : (
                        "Send OTP"
                      )}
                    </Button>
                  </>
                )}

                {formStep === 2 && (
                  <>
                    <Typography
                      sx={{
                        fontFamily: "Poppins, Helvetica",
                        color: "white",
                        fontSize: 16,
                        mb: 1,
                        textAlign: "center",
                      }}
                    >
                      Enter the OTP sent to {formData.email} and create a new
                      password
                    </Typography>

                    <TextField
                      name="otp"
                      placeholder="Enter OTP*"
                      value={formData.otp}
                      onChange={(e) => {
                        // Only allow numbers
                        const value = e.target.value.replace(/[^0-9]/g, "");
                        if (value.length <= 6) {
                          setFormData({
                            ...formData,
                            otp: value,
                          });
                        }

                        // Clear error
                        if (errors.otp) {
                          setErrors((prev) => {
                            const newErrors = { ...prev };
                            delete newErrors.otp;
                            return newErrors;
                          });
                        }
                      }}
                      fullWidth
                      variant="outlined"
                      error={!!errors.otp}
                      helperText={errors.otp}
                      // Limit input to 6 characters in the onChange handler instead
                      sx={{
                        bgcolor: "white",
                        borderRadius: 1,
                        "& .MuiOutlinedInput-root": {
                          height: 48,
                        },
                        mb: 2,
                      }}
                    />

                    <TextField
                      name="newPassword"
                      type="password"
                      placeholder="New Password*"
                      value={formData.newPassword}
                      onChange={handleChange}
                      fullWidth
                      variant="outlined"
                      error={!!errors.newPassword}
                      helperText={errors.newPassword}
                      sx={{
                        bgcolor: "white",
                        borderRadius: 1,
                        "& .MuiOutlinedInput-root": {
                          height: 48,
                        },
                      }}
                    />

                    {formData.newPassword && (
                      <Box sx={{ width: "100%" }}>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                          }}
                        >
                          <Typography
                            variant="caption"
                            sx={{ color: "white", fontSize: 12 }}
                          >
                            Password Strength:
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{
                              color: getStrengthColor(passwordStrength),
                              fontWeight: "bold",
                              fontSize: 12,
                            }}
                          >
                            {getStrengthLabel(passwordStrength)}
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            width: "100%",
                            height: 4,
                            bgcolor: "rgba(255,255,255,0.2)",
                            borderRadius: 2,
                            mt: 0.5,
                            overflow: "hidden",
                          }}
                        >
                          <Box
                            sx={{
                              height: "100%",
                              width: `${(passwordStrength / 5) * 100}%`,
                              bgcolor: getStrengthColor(passwordStrength),
                              transition: "width 0.3s ease",
                            }}
                          />
                        </Box>
                        <FormHelperText
                          sx={{
                            color: "white",
                            mt: 1,
                            fontSize: 12,
                            opacity: 0.8,
                          }}
                        >
                          Password must contain at least 8 characters, one
                          uppercase letter, one lowercase letter, one number,
                          and one special character.
                        </FormHelperText>
                      </Box>
                    )}

                    <TextField
                      name="confirmPassword"
                      type="password"
                      placeholder="Confirm Password*"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      fullWidth
                      variant="outlined"
                      error={!!errors.confirmPassword}
                      helperText={errors.confirmPassword}
                      sx={{
                        bgcolor: "white",
                        borderRadius: 1,
                        "& .MuiOutlinedInput-root": {
                          height: 48,
                        },
                      }}
                    />

                    <Button
                      type="button"
                      variant="contained"
                      fullWidth
                      onClick={handleResetPassword}
                      disabled={
                        isSubmitting ||
                        !formData.otp ||
                        formData.otp.length < 4 ||
                        !formData.newPassword ||
                        !formData.confirmPassword ||
                        passwordStrength < 5
                      }
                      sx={{
                        bgcolor: "white",
                        color: "black",
                        height: 48,
                        borderRadius: 1,
                        textTransform: "none",
                        fontFamily: "Poppins, Helvetica",
                        fontWeight: 500,
                        fontSize: 16,
                        mt: 1,
                        "&:hover": {
                          bgcolor: "#f0f0f0",
                        },
                      }}
                    >
                      {isSubmitting ? (
                        <CircularProgress size={24} color="inherit" />
                      ) : (
                        "Reset Password"
                      )}
                    </Button>

                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        width: "100%",
                        mt: 2,
                      }}
                    >
                      <Button
                        type="button"
                        variant="text"
                        onClick={() => setFormStep(1)}
                        sx={{
                          color: "white",
                          textTransform: "none",
                          fontFamily: "Poppins, Helvetica",
                          fontSize: 14,
                        }}
                      >
                        <ArrowBackIcon
                          sx={{
                            fontSize: 16,
                            mr: 1,
                          }}
                        />
                        Back
                      </Button>

                      <Button
                        type="button"
                        variant="text"
                        onClick={handleSendOtp}
                        disabled={isResendDisabled}
                        sx={{
                          color: "white",
                          textTransform: "none",
                          fontFamily: "Poppins, Helvetica",
                          fontSize: 14,
                          opacity: isResendDisabled ? 0.5 : 1,
                        }}
                      >
                        {isResendDisabled
                          ? `Resend OTP in ${timer}s`
                          : "Resend OTP"}
                      </Button>
                    </Box>
                  </>
                )}
              </Stack>
            </form>

            <Box display="flex" justifyContent="center" mt={2}>
              <Link
                component="button"
                underline="always"
                onClick={() =>
                  setOpen({ ...open, login: true, forgotpassword: false })
                }
                sx={{
                  color: "white",
                  fontFamily: "Poppins, Helvetica",
                  fontSize: 16,
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <ArrowBackIcon
                  sx={{
                    fontSize: 16,
                    mr: 1,
                    color: "white",
                    ":hover": { color: "white" },
                  }}
                />
                to Log In
              </Link>
            </Box>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default ForgotPassword;
