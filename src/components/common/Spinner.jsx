import React from 'react';
import { 
  Box, 
  CircularProgress, 
  Typography, 
  useTheme 
} from '@mui/material';

const Spinner = ({
  fullScreen = false,
  message = 'Loading...',
  size = 60,
  thickness = 4,
  color = 'primary',
  backgroundColor = 'rgba(255, 255, 255, 0.5)',
  containerSx = {}
}) => {
  const theme = useTheme();

  const defaultFullScreenSx = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor,
    zIndex: theme.zIndex.modal + 1,
    backdropFilter: 'blur(4px)',
  };

  const defaultContainerSx = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing(2),
  };

  const combinedSx = fullScreen 
    ? { ...defaultFullScreenSx, ...containerSx } 
    : { ...defaultContainerSx, ...containerSx };

  return (
    <Box sx={combinedSx}>
      <CircularProgress 
        color={color}
        size={size}
        thickness={thickness}
      />
      {message && (
        <Typography 
          variant="body1" 
          sx={{ 
            marginTop: theme.spacing(2),
            fontWeight: 500,
            color: fullScreen ? 'white' : 'inherit'
          }}
        >
          {message}
        </Typography>
      )}
    </Box>
  );
};

export default Spinner;