import React, { useState, useEffect } from "react";
import {
  Button,
  Grid,
  MenuItem,
  Paper,
  Select,
  TextField,
  CircularProgress,
  Box,
  Typography,
} from "@mui/material";
import { RestartAlt, Search as SearchIcon } from "@mui/icons-material";
import { Client } from "../../api/client";
import useUserGeoInfo from "../../lib/hooks/UseGetlocation";

const ClubSearchForm = ({
  search,
  setSearch,
  handleSearch,
  loading,
  handleReset,
}) => {
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [districts, setDistricts] = useState([]);
    const [locationLoaded, setLocationLoaded] = useState(false);
  const { geoInfo } = useUserGeoInfo();

  // Load countries on component mount
  useEffect(() => {
    const fetchCountries = async () => {
      const response = await Client.get("/location/countries");
      if (!response.data.success) return;
      setCountries(response.data.data);
    };

    fetchCountries();
  }, []);

  // Load states when country changes
  useEffect(() => {
    if (search.country) {
      const countryObj = countries.find((c) => c.name === search.country);
      if (countryObj) {
        const fetchStates = async () => {
          const response = await Client.get(
            `/location/states/${countryObj.isoCode}`,
            {
              params: { country: countryObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setStates(response.data.data);
        };
        fetchStates();
      }
    } else {
      setStates([]);
    }
    // Reset dependent fields
    setSearch((prev) => ({ ...prev, state: "", district: "", city: "" }));
  }, [search.country, countries]);

  useEffect(() => {
    if (!geoInfo || countries.length === 0) return;

    const countryObj = countries.find((c) => c.isoCode === geoInfo.country);
    if (countryObj) {
      setSearch((prev) => ({ ...prev, country: countryObj.name }));

      const loadStatesAndSet = async () => {
        const stateRes = await Client.get(
          `/location/states/${countryObj.isoCode}`
        );
        if (!stateRes.data.success) return;

        setStates(stateRes.data.data);
        const stateObj = stateRes.data.data.find(
          (s) => s.name === geoInfo.state
        );

        if (stateObj) {
          setSearch((prev) => ({ ...prev, state: stateObj.name }));

          // Load districts if applicable
          if (countryObj.name === "India") {
            const distRes = await Client.get(
              `/location/districts/${stateObj.isoCode}`,
              {
                params: {
                  country: countryObj.isoCode,
                  state: stateObj.isoCode,
                },
              }
            );
            if (distRes.data.success) {
              setDistricts(distRes.data.data);
            }
          }

          // Load cities
          const cityRes = await Client.get(
            `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (cityRes.data.success) {
            setCities(cityRes.data.data);
            const cityObj = cityRes.data.data.find(
              (c) => c.name === geoInfo.city
            );
            if (cityObj) {
              setSearch((prev) => ({ ...prev, city: cityObj.name }));
            }
          }
        }
        setLocationLoaded(true);
      };

      loadStatesAndSet();
    }
  }, [geoInfo, countries,setSearch]);


    useEffect(() => {
      if (locationLoaded) {
        handleSearch(1);
        // Reset flag to prevent repeated searches
        setLocationLoaded(false);
      }
    }, [locationLoaded, handleSearch]);

  // Load districts when state changes
  useEffect(() => {
    if (search.country === "India" && search.country && search.state) {
      const countryObj = countries.find((c) => c.name === search.country);
      const stateObj = states.find((s) => s.name === search.state);
      if (countryObj && stateObj) {
        const fetchDistricts = async () => {
          const response = await Client.get(
            `/location/districts/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setDistricts(response.data.data);
        };
        fetchDistricts();
      }
    } else {
      setDistricts([]);
    }
    // Reset dependent field
    setSearch((prev) => ({ ...prev, city: "", district: "" }));
  }, [search.country, search.state, countries, states]);

  // Load cities when state changes
  useEffect(() => {
    if (search.country && search.state) {
      const countryObj = countries.find((c) => c.name === search.country);
      const stateObj = states.find((s) => s.name === search.state);
      if (countryObj && stateObj) {
        const fetchCities = async () => {
          const response = await Client.get(
            `/location/cities/${countryObj.isoCode}/${stateObj.isoCode}`,
            {
              params: { country: countryObj.isoCode, state: stateObj.isoCode },
            }
          );
          if (!response.data.success) return;
          setCities(response.data.data);
        };
        fetchCities();
      }
    } else {
      setCities([]);
      setSearch((prev) => ({ ...prev, district: "" }));
    }
    // Reset dependent field
    setSearch((prev) => ({ ...prev, city: "" }));
  }, [search.country, search.state, countries, states]);

  // Handle input changes without triggering search
  const handleInputChange = (field, value) => {
    setSearch((prev) => ({ ...prev, [field]: value }));
  };

  // Handle key press in input fields (for Enter key)
  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSearch();
    }
  };

  return (
    <Paper sx={{ mb: 3, p: 2, bgcolor: "#f9f9f9" }}>
      <Box>
        <Typography variant="h5" sx={{ color: "#3f51b5" }}>
          Search Clubs
        </Typography>
      </Box>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Club Name"
            variant="outlined"
            name="clubName"
            fullWidth
            size="small"
            value={search.ClubName}
            onChange={(e) => handleInputChange("clubName", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
          />
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.country}
            onChange={(e) => handleInputChange("country", e.target.value)}
            renderValue={(selected) => (selected ? selected : "Country")}
            sx={{ bgcolor: "white" }}
          >
            <MenuItem value="">
               Country 
            </MenuItem>
            {countries.map((country) => (
              <MenuItem key={country.isoCode} value={country.name}>
                {country.name}
              </MenuItem>
            ))}
          </Select>
        </Grid>
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.state}
            onChange={(e) => handleInputChange("state", e.target.value)}
            renderValue={(selected) => (selected ? selected : "State")}
            sx={{ bgcolor: "white" }}
            disabled={!search.country}
          >
            <MenuItem value="">
               State 
            </MenuItem>
            {states.map((state) => (
              <MenuItem key={state.isoCode} value={state.name}>
                {state.name}
              </MenuItem>
            ))}
          </Select>
        </Grid>
        {search.country === "India" && (
          <Grid item xs={12} sm={4} md={3} lg={2}>
            <Select
              fullWidth
              displayEmpty
              size="small"
              value={search.district}
              onChange={(e) => handleInputChange("district", e.target.value)}
              renderValue={(selected) => (selected ? selected : "District")}
              sx={{ bgcolor: "white" }}
              disabled={!search.state}
            >
              <MenuItem value="">
                 district 
              </MenuItem>
              {districts.map((district) => (
                <MenuItem
                  key={district.id || district.name}
                  value={district.name}
                >
                  {district.name}
                </MenuItem>
              ))}
            </Select>
          </Grid>
        )}
        {(search.country !== "India" || search.country === "") && (
          <Grid item xs={12} sm={4} md={3} lg={2}>
            <TextField
              placeholder="District"
              variant="outlined"
              name="district"
              fullWidth
              size="small"
              value={search.district}
              onChange={(e) => handleInputChange("district", e.target.value)}
              onKeyPress={handleKeyPress}
              sx={{ bgcolor: "white" }}
              disabled={!search.state}
            />
          </Grid>
        )}
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.city}
            onChange={(e) => handleInputChange("city", e.target.value)}
            renderValue={(selected) => (selected ? selected : "City")}
            sx={{ bgcolor: "white" }}
            disabled={!search.state}
          >
            <MenuItem value="">
               City 
            </MenuItem>
            {cities.map((city) => (
              <MenuItem key={city.id || city.name} value={city.name}>
                {city.name}
              </MenuItem>
            ))}
          </Select>
        </Grid>

        <Grid
          item
          xs={12}
          sm={4}
          md={9}
          lg={2}
          sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}
        >
          <Button
            variant="containedSecondary"
            color="secondary"
            sx={{
              width: "40px",
              minWidth: "40px !important",
            }}
            onClick={handleReset}
            disabled={loading}
          >
            <RestartAlt />
          </Button>
          <Button
            variant="contained"
            color="primary"
            fullWidth
            onClick={handleSearch}
            disabled={loading}
            startIcon={
              loading ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <SearchIcon />
              )
            }
            sx={{
              bgcolor: "#3f51b5",
              textTransform: "none",
              height: "40px",
              fontSize: "16px",
              maxWidth: {
                xs: "100%",
                sm: "150px",
              },
            }}
          >
            Search
          </Button>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default ClubSearchForm;
