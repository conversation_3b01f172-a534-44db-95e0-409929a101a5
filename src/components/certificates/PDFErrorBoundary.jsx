import React from 'react';
import { Typography, Box } from '@mui/material';

class PDFErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI.
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // You can also log the error to an error reporting service
    console.error('PDF Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%',
            padding: 3,
            textAlign: 'center'
          }}
        >
          <Typography variant="h6" color="error" gutterBottom>
            Certificate Template Error
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            There was an error rendering the certificate template.
          </Typography>
          {this.state.error && (
            <Typography variant="caption" color="text.secondary">
              Error: {this.state.error.toString()}
            </Typography>
          )}
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
            Please try selecting a different template or refresh the page.
          </Typography>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default PDFErrorBoundary;