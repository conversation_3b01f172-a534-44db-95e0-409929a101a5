import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Button,
  Skeleton,
  Paper,
  Divider,
  Chip,
  CircularProgress,
  Badge,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Person as PersonIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import EmptyState from "../common/EmptyState";
import { Link } from "react-router-dom";

/**
 * Component for displaying and managing player join requests to a club
 * @param {Object} props - Component props
 * @param {boolean} props.loading - Initial loading state
 */
const JoinRequestsTab = ({ loading: initialLoading }) => {
  const [joinRequests, setJoinRequests] = useState([]);
  const [loading, setLoading] = useState(initialLoading);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const toast = UseToast();

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "";

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // Format the full date for display in tooltip
    const fullDateFormatted = date.toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });

    // Format for relative time display
    let relativeTime;
    if (diffMins < 60) {
      relativeTime = `${diffMins} minute${diffMins !== 1 ? "s" : ""} ago`;
    } else if (diffHours < 24) {
      relativeTime = `${diffHours} hour${diffHours !== 1 ? "s" : ""} ago`;
    } else if (diffDays < 7) {
      relativeTime = `${diffDays} day${diffDays !== 1 ? "s" : ""} ago`;
    } else {
      relativeTime = date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    }

    return { relativeTime, fullDateFormatted };
  };

  // Fetch join requests from API
  const fetchJoinRequests = useCallback(async (pageNum = 1) => {
    setLoading(true);

    try {
      const response = await Client.get("/club/members/join-request", {
        params: { page: pageNum, limit: 10 },
      });

      if (response.data.success) {
        if (response.status === 204) {
          setJoinRequests([]);
        }
        const newRequests = response.data.data || [];
        setJoinRequests(
          pageNum === 1 ? newRequests : [...joinRequests, ...newRequests]
        );
        setHasMore(newRequests.length === 10);
      }
    } catch (error) {
      console.error("Error fetching join requests:", error);
      toast.error("An error occurred while fetching join requests");
    } finally {
      setLoading(false);
    }
  }, []);

  // Refresh join requests
  const refreshJoinRequests = async () => {
    setRefreshing(true);
    setPage(1);
    await fetchJoinRequests(1);
    setRefreshing(false);
  };

  // Load more join requests
  const loadMore = () => {
    if (hasMore && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchJoinRequests(nextPage);
    }
  };

  // Handle join request action (accept/reject)
  const handleRequestAction = async (action, requestId, playerId) => {
    try {
      const response = await Client.post("/club/members/join-request", {
        action,
        requestId,
        playerId,
      });

      if (response.data.success) {
        toast.success(
          `Join request ${
            action === "accept" ? "accepted" : "rejected"
          } successfully`
        );

        // Remove the request from the list
        setJoinRequests((prevRequests) =>
          prevRequests.filter((request) => request.id !== requestId)
        );
      } else {
        toast.error(
          response.data.message || `Failed to ${action} join request`
        );
      }
    } catch (error) {
      console.error(`Error ${action}ing join request:`, error);
      if (error.response?.status === 409) {
        toast.error(error.response.data.error);
        return;
      }
      toast.error(`An error occurred while ${action}ing join request`);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchJoinRequests();
  }, [fetchJoinRequests]);

  // Get unread requests count
  const unreadRequestsCount = joinRequests.filter((req) => !req.isRead).length;

  // Loading skeleton
  if (loading && joinRequests.length === 0) {
    return (
      <Box>
        {Array(2)
          .fill(0)
          .map((_, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <Skeleton
                    variant="circular"
                    width={56}
                    height={56}
                    sx={{ mr: 2 }}
                  />
                  <Box sx={{ width: "100%" }}>
                    <Skeleton variant="text" height={18} width="40%" />
                    <Skeleton variant="text" height={20} width="70%" />
                  </Box>
                </Box>
              </Paper>
            </Box>
          ))}
      </Box>
    );
  }

  // Empty state
  if (joinRequests.length === 0 && !loading) {
    return (
      <EmptyState
        icon={<PersonIcon sx={{ fontSize: 60, color: "text.secondary" }} />}
        title="No Join Requests"
        description="You don't have any player join requests at the moment."
        action={
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={refreshJoinRequests}
            disabled={refreshing}
            sx={{ fontSize: 16 }}
          >
            Refresh
          </Button>
        }
      />
    );
  }

  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <PersonIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6">
            Player Join Requests
            {unreadRequestsCount > 0 && (
              <Badge
                badgeContent={unreadRequestsCount}
                color="error"
                sx={{ ml: 2 }}
              />
            )}
          </Typography>
        </Box>
        <Tooltip title="Refresh requests">
          <IconButton
            onClick={refreshJoinRequests}
            disabled={refreshing}
            size="small"
          >
            {refreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      <Paper
        elevation={0}
        variant="outlined"
        sx={{ borderRadius: 1, overflow: "hidden" }}
      >
        <List sx={{ width: "100%", p: 0 }}>
          {joinRequests.map((request, index) => {
            const player = request.metadata?.player || {};

            return (
              <React.Fragment key={request.id || index}>
                <ListItem
                  alignItems="flex-start"
                  sx={{
                    "&:hover": { bgcolor: "rgba(0, 0, 0, 0.04)" },
                    py: 2,
                    px: { xs: 1, sm: 2 },
                    transition: "background-color 0.2s ease",
                    bgcolor: !request.isRead
                      ? "rgba(25, 118, 210, 0.05)"
                      : "transparent",
                  }}
                >
                  <ListItemAvatar>
                    <Avatar
                      src={player.profilePicture}
                      alt={player.playerName}
                      sx={{
                        width: 56,
                        height: 56,
                        mr: 2,
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                      }}
                    >
                      <PersonIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: { xs: "column", sm: "row" },
                          alignItems: { xs: "flex-start", sm: "center" },
                          mb: 0.5,
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          fontWeight="medium"
                          component={Link}
                          to={`/players/${player.cbid}`}
                          sx={{
                            textDecoration: "none",
                            color: "primary.main",
                            "&:hover": { textDecoration: "underline" },
                          }}
                        >
                          {player.playerName ||
                            request.title ||
                            "Player Join Request"}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <React.Fragment>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 0.5,
                          }}
                        >
                          <Typography
                            variant="caption"
                            color="text.primary"
                            component="span"
                            title={
                              formatDate(request.createdAt).fullDateFormatted
                            }
                            sx={{
                              cursor: "help",
                              borderBottom: "1px dotted",
                              borderColor: "text.disabled",
                            }}
                          >
                            {formatDate(request.createdAt).relativeTime}
                          </Typography>
                          <Chip
                            label="New"
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{
                              height: 18,
                              fontSize: "0.6rem",
                              ml: 1,
                              display: request.isRead ? "none" : "inline-flex",
                            }}
                          />
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "row",
                            gap: 1,
                            alignItems: "center",
                            justifyContent: "space-between",
                          }}
                        >
                          <Typography
                            variant="body2"
                            color="text.primary"
                            component="span"
                            display="flex"
                            sx={{
                              mb: 1,
                              mt: 0.5,
                              textAlign: "start",
                            }}
                          >
                            {request.metadata.message ||
                              "Requested to join your club"}
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              flexDirection: { xs: "column", sm: "row" },
                              alignItems: { xs: "flex-start", sm: "center" },
                            }}
                          >
                            {request.status === "pending" && (
                              <Box
                                sx={{
                                  mt: { xs: 1, sm: 0 },
                                  display: "flex",
                                  gap: 1,
                                }}
                              >
                                <Button
                                  variant="contained"
                                  size="small"
                                  startIcon={<CheckIcon />}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleRequestAction(
                                      "accept",
                                      request.id,
                                      player.playerId
                                    );
                                  }}
                                  color="success"
                                  disableElevation
                                >
                                  Accept
                                </Button>
                                <Button
                                  variant="outlined"
                                  size="small"
                                  startIcon={<CloseIcon />}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleRequestAction(
                                      "reject",
                                      request.id,
                                      player.playerId
                                    );
                                  }}
                                  color="error"
                                >
                                  Reject
                                </Button>
                              </Box>
                            )}
                          </Box>
                        </Box>
                      </React.Fragment>
                    }
                  />
                </ListItem>
                {index < joinRequests.length - 1 && <Divider component="li" />}
              </React.Fragment>
            );
          })}
        </List>
      </Paper>

      {hasMore && (
        <Box sx={{ textAlign: "center", mt: 2 }}>
          <Button
            variant="outlined"
            onClick={loadMore}
            disabled={loading}
            startIcon={loading && <CircularProgress size={16} />}
          >
            {loading ? "Loading..." : "Load More"}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default JoinRequestsTab;
