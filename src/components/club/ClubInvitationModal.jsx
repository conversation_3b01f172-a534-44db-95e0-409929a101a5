import React, { useState } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Typography,
  CircularProgress,
} from "@mui/material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

/**
 * Modal for sending club invitations to players or player join requests to clubs
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the modal is open
 * @param {Function} props.onClose - Function to close the modal
 * @param {Object} props.playerData - Player data if available (for club inviting player)
 * @param {Object} props.clubData - Club data if available (for player requesting to join)
 * @param {string} props.mode - Mode of operation: "club-invite" or "join-request"
 */
const ClubInvitationModal = ({
  open,
  onClose,
  playerData,
  clubData,
  mode = "club-invite",
}) => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const toast = UseToast();

  // Determine if this is a club inviting a player or a player requesting to join a club
  const isClubInvite = mode === "club-invite";
  const isJoinRequest = mode === "join-request";

  // Handle sending the invitation or join request
  const handleSendInvitation = async () => {
    setLoading(true);
    try {
      let payload;
      let endpoint;

      if (isClubInvite) {
        // Club inviting a player
        payload = {
          message: message || "You have been invited to join their chess club.",
          playerId: playerData?.cbid || null,
          playerName: playerData?.name || null,
        };
        endpoint = "/club/invite";
      } else {
        // Player requesting to join a club
        payload = {
          message: message || "A request to join your chess club has been made",
          clubId: clubData?.clubId || null,
          clubName: clubData?.clubName || null,
        };
        endpoint = "/user/club-join-request";
      }

      const response = await Client.post(endpoint, payload);

      if (response.data.success) {
        toast.success(
          isClubInvite
            ? "Invitation sent successfully"
            : "Join request sent successfully"
        );
        setMessage("");
        onClose();
      }
    } catch (error) {
      console.error(
        `Error sending ${isClubInvite ? "invitation" : "join request"}:`,
        error
      );
      if (error.response?.status === 409) {
        toast.error(error.response.data.error);
        setMessage("");
        return;
      }
      toast.error(
        error.response?.data?.message ||
          `An error occurred while sending the ${
            isClubInvite ? "invitation" : "join request"
          }`
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={loading ? null : onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{ bgcolor: "#f5f5f5", fontWeight: "bold" }}>
        {isClubInvite ? "Send Club Invitation" : "Request to Join Club"}
      </DialogTitle>

      <DialogContent sx={{ pt: 2, pb: 1 }}>
        <DialogContentText sx={{ mb: 2, color: "black" }}>
          {isClubInvite
            ? "Send an invitation to join your club."
            : "Send a request to join this club."}
        </DialogContentText>

        {/* Player Information (if available for club invite) */}
        {isClubInvite && playerData && (
          <Box sx={{ mb: 3, p: 2, bgcolor: "#f9f9f9", borderRadius: 1 }}>
            <Typography
              variant="subtitle1"
              fontWeight="bold"
              sx={{ textAlign: "start" }}
            >
              Player Information
            </Typography>
            <Typography variant="body1">
              Name: {playerData.name || "Not specified"}
            </Typography>
            <Typography variant="body1">
              CBID: {playerData.cbid || "Not specified"}
            </Typography>
          </Box>
        )}

        {/* Club Information (if available for join request) */}
        {isJoinRequest && clubData && (
          <Box sx={{ mb: 3, p: 2, bgcolor: "#f9f9f9", borderRadius: 1 }}>
            <Typography
              variant="subtitle1"
              fontWeight="bold"
              sx={{ textAlign: "start" }}
            >
              Club Information
            </Typography>
            <Typography variant="body1">
              Name: {clubData.clubName || "Not specified"}
            </Typography>
          </Box>
        )}

        {/* Message Input */}
        <TextField
          autoFocus
          label={isClubInvite ? "Invitation Message" : "Join Request Message"}
          fullWidth
          multiline
          rows={4}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder={
            isClubInvite
              ? "Enter your invitation message here..."
              : "Enter your request message here..."
          }
          inputProps={{
            maxLength: 50,
          }}
          onKeyPress={(e) => {
            const regex = /^[a-zA-Z@.,\s]$/;
            if (!regex.test(e.key)) {
              e.preventDefault();
            }
          }}
          disabled={loading}
          sx={{ mb: 2, "& .MuiInputLabel-root": { color: "black" } }}
        />
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2, fontSize: 16 }}>
        <Button
          onClick={onClose}
          disabled={loading}
          variant="outlined"
          sx={{ fontSize: 16 }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSendInvitation}
          variant="contained"
          color="primary"
          disabled={loading}
          sx={{ fontSize: 16 }}
          startIcon={loading && <CircularProgress size={20} color="inherit" />}
        >
          {loading
            ? "Sending..."
            : isClubInvite
            ? "Send Invitation"
            : "Send Join Request"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ClubInvitationModal;
