import React, { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Typography,
  Box,
  CircularProgress,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import PreviewLoad from "./PreviewLoad";

// Dashboard card data
const dashboardCards = [
  { title: "", color: "#ebccff", icon: '', link: "#" },
  { title: "", color: "#FFD1DC", icon:'', link: "#" },
  { title: "", color: "#fac67c", icon:' ', link: "#" },
  { title: "", color: "#e7d17b", icon: '', link: "#" },
  { title: "", color: "#a5d8c6", icon: '', link: "#" },
  { title: "", color: "#beddf0", icon: '', link: "#" },
  { title: "", color: "#f3beb9", icon: '', link: "#" },
  { title: "", color: "#f0e6be", icon: '', link: "#" },
  { title: "", color: "#c4b8f0", icon: '', link: "#" },
  { title: "", color: "#fad0e8", icon: '', link: "#" },
];


const ImagePreview = ({ open, onClose,Link,Type }) => {
  const [loading, setLoading] = useState(false);
  console.log("url in popup ",Link)

//   useEffect(() => {
//     const getProfileData = async () => {
//       if (user && !currentProfile && !authLoading) {
//         setLoading(true);
//         try {
//           await fetchProfileData();
//         } catch (error) {
//           console.error("Error fetching profile:", error);
//         } finally {
//           setLoading(false);
//         }
//       }
//     };
//     getProfileData();
//   }, [user, currentProfile, authLoading]);

  return (
    <Dialog sx={{filter: "brightness(0.85) contrast(0.95)",}} open={open} onClose={onClose} fullWidth maxWidth="lg" scroll="paper" >
      <DialogTitle>
        Club Dashboard
        <IconButton onClick={onClose} sx={{ position: "absolute", right: 16, top: 16 }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" height="30vh">
            <CircularProgress />
          </Box>
        ) : (
          <Box  sx={{ px: "1vw", py: "1vh",  }}>
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Welcome,user!
            </Typography>

            <PreviewLoad links={dashboardCards} Url={Link} type={Type}/>
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ImagePreview;