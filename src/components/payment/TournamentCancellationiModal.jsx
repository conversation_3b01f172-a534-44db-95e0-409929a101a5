import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Divider,
  IconButton,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const TournamentCancellationModal = ({ 
  open, 
  onClose, 
  tournament, 
  onCancellationSuccess 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [cancellationData, setCancellationData] = useState({
    reason: "",
    comments: "",
    processRefunds: true,
  });
  const [formErrors, setFormErrors] = useState({});
  const toast = UseToast();
  const messageId = "6f7edec7-006f-479b-bfe4-b1f444eafe84";

  const validateForm = () => {
    const errors = {};
    if (!cancellationData.reason.trim()) {
      errors.reason = "Reason is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    setCancellationData((prev) => ({
      ...prev,
      [name]: name === "processRefunds" ? checked : value,
    }));

    // Clear error for the field being changed
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: null,
      }));
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError(null);

    try {
      const response = await Client.post(`/tournament/${tournament.title}/cancel`, {
        tournamentId: tournament.id,
        reason: cancellationData.reason,
        comments: cancellationData.comments,
        processRefunds: cancellationData.processRefunds,
        messageId: messageId,
      });

      if (response.data.success) {
        toast.success("Tournament cancelled successfully");

        if (onCancellationSuccess) {
          onCancellationSuccess(response.data.data);
        }
        onClose();
      } else {
        setError(response.data.message || "Failed to cancel tournament");
      }
    } catch (error) {
      console.error("Cancellation error:", error);
      setError(
        error.response?.data?.message || 
        error.response?.data?.error || 
        "An error occurred while processing the cancellation request"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={loading ? null : onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 3,
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: "#f5f5f5",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          py: 2,
        }}
      >
        <Typography variant="h6" component="div" fontWeight="bold">
          Cancel Tournament
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          disabled={loading}
          aria-label="close"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ mb: 3,mt:2 }}>
          <Typography variant="subtitle1" fontWeight="medium" sx={{ fontSize: "1.5rem" }} gutterBottom>
            Tournament Details
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "start",
              alignItems: "start",
              gap: 1,
              bgcolor: "#f9f9f9",
              p: 2,
       
              borderRadius: 1,
            }}
          >
            <Typography variant="body2">
              <strong>Tournament:</strong> {tournament?.title?.replace(/-/g, " ") || "N/A"}
            </Typography>
            <Typography variant="body2">
              <strong>Start Date:</strong> {tournament?.startDate 
                ? new Date(tournament.startDate).toLocaleDateString() 
                : "N/A"}
            </Typography>
            <Typography variant="body2">
              <strong>Entry Fee:</strong> {tournament?.entryFeeCurrency || "INR"}{" "}
              {tournament?.entryFee || "0.00"}
            </Typography>
            <Typography variant="body2">
              <strong>Location:</strong> {tournament?.location || "N/A"}
            </Typography>
          </Box>
        </Box>

        <Alert 
          severity="warning" 
          sx={{ mb: 3 }}
        >
          <Typography variant="body2" fontWeight="medium" sx={{ fontSize: "1.2rem" ,textAlign:"start"}}>
            Warning: Cancelling this tournament will:
          </Typography>
          <ul style={{ fontSize: "1.2rem" ,textAlign:"start"}}>
            <li>Notify all registered players</li>
            <li>Mark the tournament as cancelled</li>
            <li>Process refunds for all registered players (if selected)</li>
          </ul>
          <Typography variant="body2" sx={{ fontSize: "1.2rem",textAlign:"start" }}>
            This action cannot be undone.
          </Typography>
        </Alert>

        <Divider sx={{ my: 2 }} />

        <FormControl fullWidth margin="normal" error={!!formErrors.reason}>
          <InputLabel id="reason-label">Reason for Cancellation</InputLabel>
          <Select
            labelId="reason-label"
            id="reason"
            name="reason"
            value={cancellationData.reason}
            onChange={handleChange}
            label="Reason for Cancellation"
            disabled={loading}
          >
            <MenuItem value="insufficient_participants">Insufficient Participants</MenuItem>
            <MenuItem value="venue_unavailable">Venue Unavailable</MenuItem>
            <MenuItem value="scheduling_conflict">Scheduling Conflict</MenuItem>
            <MenuItem value="technical_issues">Technical Issues</MenuItem>
            <MenuItem value="weather_conditions">Weather Conditions</MenuItem>
            <MenuItem value="other">Other</MenuItem>
          </Select>
          {formErrors.reason && (
            <FormHelperText error>{formErrors.reason}</FormHelperText>
          )}
        </FormControl>

        <TextField
          fullWidth
          margin="normal"
          label="Additional Comments"
          name="comments"
          value={cancellationData.comments}
          onChange={handleChange}
          multiline
          rows={3}
          disabled={loading}
        />

        <FormControlLabel
          control={
            <Checkbox
              checked={cancellationData.processRefunds}
              onChange={handleChange}
              name="processRefunds"
              color="primary"
              disabled={loading}
            />
          }
          label="Process refunds for all registered players"
          sx={{ mt: 2 }}
        />

        <Typography variant="body2" color="text.secondary" sx={{ mt: 1,fontSize: "1.2rem" }}>
          Note: All registered players will receive a full refund if this option is selected.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, bgcolor: "#f5f5f5" }}>
        <Button
          onClick={onClose}
          color="inherit"
          disabled={loading}
          sx={{ fontWeight: "medium" }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="error"
          disabled={loading}
          startIcon={loading && <CircularProgress size={20} color="inherit" />}
          sx={{ fontWeight: "medium" }}
        >
          {loading ? "Processing..." : "Cancel Tournament"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TournamentCancellationModal;