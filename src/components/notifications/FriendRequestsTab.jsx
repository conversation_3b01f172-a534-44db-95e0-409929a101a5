import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Button,
  Skeleton,
  Paper,
  Divider,
  Chip,
} from "@mui/material";
import {
  Person as PersonIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  People as PeopleIcon,
} from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import EmptyState from "../common/EmptyState";
import { Link } from "react-router-dom";

const FriendRequestsTab = ({ loading: initialLoading }) => {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(initialLoading);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const toast = UseToast();

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "";

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // Format the full date for display in tooltip
    const fullDateFormatted = date.toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });

    // Format for relative time display
    let relativeTime;
    if (diffMins < 60) {
      relativeTime = `${diffMins} minute${diffMins !== 1 ? "s" : ""} ago`;
    } else if (diffHours < 24) {
      relativeTime = `${diffHours} hour${diffHours !== 1 ? "s" : ""} ago`;
    } else if (diffDays < 7) {
      relativeTime = `${diffDays} day${diffDays !== 1 ? "s" : ""} ago`;
    } else {
      relativeTime = date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    }

    return { relativeTime, fullDateFormatted };
  };

  // Fetch friend requests
  const fetchRequests = useCallback(async (pageNum = 1, append = false) => {
    setLoading(true);
    try {
      const response = await Client.get("/user/friend-request", {
        params: { page: pageNum, limit: 10 },
      });

      if (response.data.success) {
        const newRequests = response.data.data || [];
        setRequests(prev => append ? [...prev, ...newRequests] : newRequests);
        setHasMore(newRequests.length === 10);
      } else {
        toast.error(response.data.message || "Failed to fetch friend requests");
      }
    } catch (error) {
      console.error("Error fetching friend requests:", error);
      toast.error("An error occurred while fetching friend requests");
    } finally {
      setLoading(false);
    }
  }, []);

  // Load more requests
  const loadMore = () => {
    if (hasMore && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchRequests(nextPage, true);
    }
  };

  // Accept friend request
  const acceptRequest = async (friendId) => {
    try {
      const response = await Client.post(`/user/friend-request/update`, {
        friendId,
        action: "accept"
      });

      if (response.data.success) {
        // Remove the request from the list after accepting
        setRequests(prev => prev.filter(request => request.metadata.friend.friendId !== friendId));
        toast.success("Friend request accepted");
      } else {
        toast.error(response.data.message || "Failed to accept friend request");
      }
    } catch (error) {
      console.error("Error accepting friend request:", error);
      toast.error("An error occurred while accepting friend request");
    }
  };

  // Reject friend request
  const rejectRequest = async (friendId) => {
    try {
      const response = await Client.post(`/friends/update`, {
        friendId,
        action: "reject"
      });

      if (response.data.success) {
        // Remove the request from the list after rejecting
        setRequests(prev => prev.filter(request => request.metadata.friend.friendId !== friendId));
        toast.success("Friend request rejected");
      } else {
        toast.error(response.data.message || "Failed to reject friend request");
      }
    } catch (error) {
      console.error("Error rejecting friend request:", error);
      toast.error("An error occurred while rejecting friend request");
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchRequests();
  }, []);

  // Loading skeleton
  if (loading && requests.length === 0) {
    return (
      <Box>
        {Array(5).fill(0).map((_, index) => (
          <Box key={index} sx={{ mb: 2 }}>
            <Skeleton variant="rectangular" height={100} />
          </Box>
        ))}
      </Box>
    );
  }

  // Empty state
  if (requests.length === 0 && !loading) {
    return (
      <EmptyState
        icon={<PeopleIcon sx={{ fontSize: 60, color: "text.primary" }} />}
        title="No Friend Requests"
        description="You don't have any friend requests at the moment."
      />
    );
  }

  return (
    <Box>
      <Paper elevation={0} variant="outlined">
        <List sx={{ width: "100%" }}>
          {requests.map((request, index) => (
            <React.Fragment key={request.id || index}>
              <ListItem
                alignItems="flex-start"
                sx={{
                  "&:hover": { bgcolor: "rgba(0, 0, 0, 0.04)" },
                  py: 2,
                }}
              >
                <ListItemAvatar>
                  <Avatar
                    src={request.metadata?.friend?.profilePicture}
                    alt={request.metadata?.friend?.friendName}
                    sx={{ width: 56, height: 56, mr: 2 }}
                  >
                    <PersonIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box sx={{ display: "flex", alignItems: "center", mb: 0.5 }}>
                      <Typography
                        variant="subtitle1"
                        fontWeight="medium"
                        component={Link}
                        to={`/players/${request.metadata?.friend?.cbid || ""}`}
                        sx={{ textDecoration: "none", color: "inherit" }}
                      >
                        {request.metadata?.friend?.friendName || "Unknown Player"}
                      </Typography>
                      {request.status && (
                        <Chip
                          label={request.status}
                          size="small"
                          color={request.status === "accepted" ? "success" : request.status === "rejected" ? "error" : "default"}
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    <React.Fragment>
                      <Typography
                        variant="body2"
                        color="text.primary"
                        component="span"
                        display="block"
                        sx={{ mb: 1, textAlign: "start" }}
                      >
                        {request.message || "Sent you a friend request"}
                      </Typography>
                      <Box
                        component="span"
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 0.5
                        }}
                      >
                        <Typography
                          variant="caption"
                          color="text.primary"
                          component="span"
                          title={formatDate(request.createdAt).fullDateFormatted}
                          sx={{
                            cursor: "help",
                            borderBottom: "1px dotted",
                            borderColor: "text.disabled"
                          }}
                        >
                          {formatDate(request.createdAt).relativeTime}
                        </Typography>
                        <Chip
                          label="New"
                          size="small"
                          color="primary"
                          variant="outlined"
                          sx={{
                            height: 18,
                            fontSize: '0.6rem',
                            display: request.isRead ? 'none' : 'inline-flex'
                          }}
                        />
                      </Box>

                      {request.status === "pending" && (
                        <Box sx={{ mt: 2, display: "flex", gap: 1, justifyContent: "flex-end", width: "100%" }}>
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<CheckIcon />}
                            onClick={() => acceptRequest(request.metadata.friend.friendId)}
                            color="success"
                          >
                            Accept
                          </Button>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<CloseIcon />}
                            onClick={() => rejectRequest(request.metadata.friend.friendId)}
                            color="error"
                          >
                            Reject
                          </Button>
                        </Box>
                      )}
                    </React.Fragment>
                  }
                />
              </ListItem>
              {index < requests.length - 1 && <Divider component="li" />}
            </React.Fragment>
          ))}
        </List>
      </Paper>

      {hasMore && (
        <Box sx={{ textAlign: "center", mt: 2 }}>
          <Button
            variant="outlined"
            onClick={loadMore}
            disabled={loading}
          >
            {loading ? "Loading..." : "Load More"}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default FriendRequestsTab;
