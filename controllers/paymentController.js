const { v4: uuidv4 } = require("uuid");
const { config } = require("../config/config");
const { sendResponse, handleError } = require("../utils/apiResponse");
const {
  createOrder,
  verifyPaymentSignature,
  fetchPaymentDetails,
  processWebhookEvent,
  verifyWebhookSignature,
} = require("../utils/razorPay");
const {
  Payment,
  Registration,
  Tournament,
  User,
  PlayerDetail,
  ClubDetail,
  Notifications,
  BulkRegistration,
} = require("../config/db").models;
const emailService = require("../utils/mailer/emailService");
const { sequelize } = require("../config/db");
const { z } = require("zod");
const { Op } = require("sequelize");
const { CheckEligibleCategory } = require("../utils/elegibleCategory");
const smsService = require("../utils/sms/smsService");
const notificationService = require("../services/notificationService");
const cronService = require("../services/cronService");

const createPaymentOrder = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { tournamentId, ageCategory, genderCategory } = req.body;

    if (!userId || !tournamentId || !ageCategory || !genderCategory) {
      return sendResponse(res, 400, {
        success: false,
        error: "Missing required parameters",
      });
    }

    // Get user details with optimized query
    const user = await User.findOne({
      where: { id: userId },
      include: [
        {
          model: PlayerDetail,
          attributes: ["dob", "gender"],
        },
      ],
    });

    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }

    // Get tournament details
    const tournament = await Tournament.findOne({
      where: { id: tournamentId },
    });

    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }

    const { eligible, reason } = CheckEligibleCategory(
      tournament.tournamentCategory, // Fixed variable assignment
      user.PlayerDetail.dob,
      user.PlayerDetail.gender,
      ageCategory,
      genderCategory
    );

    if (!eligible) {
      return sendResponse(res, 400, {
        success: false,
        error: reason || "You are not eligible for this tournament",
        action: "notEligible",
      });
    }

    // Generate unique receipt ID
    const receipt = `CB-${uuidv4().substring(0, 8)}`;

    const amount = parseFloat(tournament.entryFee);

    const productinfo = `Registration for ${tournament.title}`;

    // Create payment record in database with proper field mapping
    const payment = await Payment.create({
      userId: userId,
      tournamentId,
      paymentTransactionId: receipt,
      paymentAmount: amount,
      paymentCurrency: "INR",
      paymentType: "player",
      paymentStatus: "pending", // Use enum value directly
      paymentMethod: "Razorpay",
      paymentRemarks: `Payment for ${tournament.title}`,
      paymentDate: new Date(), // Explicitly set payment date
    });

    // Create Razorpay order
    const orderData = {
      amount: amount,
      currency: "INR",
      receipt: receipt,
      notes: {
        userId: userId.toString(),
        tournamentId: tournamentId.toString(),
        paymentId: payment.id.toString(),
        tournamentTitle: tournament.title,
        eligibleCategory: `${genderCategory}-${ageCategory}`,
        playerName: user.name,
        playerEmail: user.email,
        playerPhone: user.phoneNumber,
      },
    };

    const orderResult = await createOrder(orderData);

    if (!orderResult.success) {
      // Delete the created payment record if order creation fails
      await Payment.destroy({ where: { id: payment.id } });

      return sendResponse(res, 400, {
        success: false,
        error: "Failed to create payment order",
        details: orderResult.error,
      });
    }

    // Update payment with Razorpay order ID and status
    await payment.update({
      razorpayOrderId: orderResult.order.id,
      razorpayOrderStatus: "created", // Use enum value from model
      paymentStatus: "created", // Update status to created
    });

    // Generate checkout options for frontend
    const checkoutOptions = {
      key: config.razorpay_key_id,
      order_id: orderResult.order.id,
      amount: orderResult.order.amount, // Amount in paise
      currency: orderResult.order.currency,
      name: "Tournament Registration",
      description: productinfo,
      image: config.company_logo || "",
      prefill: {
        name: user.name,
        email: user.email,
        contact: user.phoneNumber,
      },
      theme: {
        color: "#3399cc",
      },
      notes: orderResult.order.notes,
    };

    return sendResponse(res, 200, {
      success: true,
      message: "Payment order created successfully",
      data: {
        paymentId: payment.id,
        orderId: orderResult.order.id,
        checkoutOptions,
        tournamentTitle: tournament.title,
        amount: amount, // Return amount in rupees for frontend
      },
    });
  } catch (error) {
    console.error("Error in createPaymentOrder:", error);
    handleError(res, error);
  }
};

/**
 * Verify payment after successful payment on frontend
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const verifyPayment = async (req, res) => {
  const t = await sequelize.transaction();

  try {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
      req.body;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      await t.rollback();
      return sendResponse(res, 400, {
        success: false,
        error: "Missing payment verification parameters",
      });
    }

    // Verify payment signature
    const isValid = verifyPaymentSignature({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
    });

    if (!isValid) {
      await t.rollback();
      console.error("Invalid payment signature");
      return sendResponse(res, 400, {
        success: false,
        error: "Payment verification failed",
        action: "verification_failed",
      });
    }

    // Get payment details from Razorpay
    const paymentDetailsResult = await fetchPaymentDetails(razorpay_payment_id);

    if (!paymentDetailsResult.success) {
      await t.rollback();
      return sendResponse(res, 400, {
        success: false,
        error: "Failed to fetch payment details",
        details: paymentDetailsResult.error,
      });
    }

    const paymentDetails = paymentDetailsResult.payment;
    const { notes } = paymentDetails;

    // Find payment record with proper field names
    const payment = await Payment.findOne({
      where: {
        razorpayOrderId: razorpay_order_id,
        id: notes.paymentId,
      },
      transaction: t,
    });

    if (!payment) {
      await t.rollback();
      throw new Error("Payment record not found");
    }

    // Map Razorpay status to your enum values
    const getPaymentStatus = (razorpayStatus) => {
      switch (razorpayStatus) {
        case "captured":
          return "captured";
        case "authorized":
          return "authorized";
        case "failed":
          return "failed";
        default:
          return "pending";
      }
    };

    // Update payment record with all Razorpay fields
    await payment.update(
      {
        paymentStatus: getPaymentStatus(paymentDetails.status),
        paymentMethod: paymentDetails.method || "Razorpay",
        paymentReference: razorpay_payment_id,
        razorpayPaymentId: razorpay_payment_id,
        razorpaySignature: razorpay_signature,
        razorpayOrderStatus:
          paymentDetails.status === "captured" ? "paid" : "attempted",
        razorpayAttempts: (payment.razorpayAttempts || 0) + 1,
        paymentRemarks: `Payment ${paymentDetails.status}`,
        razorpayResponse: paymentDetails, // Store complete response
      },
      { transaction: t }
    );

    let tournament, registration;

    if (paymentDetails.status === "captured") {
      // Get user and tournament details
      const user = await User.findByPk(notes.userId, { transaction: t });
      tournament = await Tournament.findByPk(notes.tournamentId, {
        include: [
          {
            model: User,
            as: "club",
            attributes: ["name"],
          },
        ],
        transaction: t,
      });

      if (!tournament) {
        await t.rollback();
        throw new Error("Tournament not found");
      }

      const [genderCategory, ageCategory] = notes.eligibleCategory.split("-");
      const tournamentName = tournament.title;
      const clubName = tournament.club.name;
      const tournamentDate = tournament.startDate;

      const registrationCount = await Registration.count({
        where: {
          tournamentId: notes.tournamentId,
        },
        transaction: t,
      });

      // Generate registration ID
      const regId = generateRegistrationId(
        tournamentName,
        clubName,
        tournamentDate,
        registrationCount
      );

      // Create registration
      registration = await Registration.create(
        {
          tournamentId: notes.tournamentId,
          playerId: notes.userId,
          paymentId: payment.id, // Use correct field name
          regId: regId,
          paymentAmount: paymentDetails.amount / 100, // Convert from paise to rupees
          genderCategory,
          ageCategory,
          tournamentTitle: notes.tournamentTitle,
          paymentDate: new Date(),
        },
        { transaction: t }
      );

      await payment.update(
        {
          registrationId: registration.id,
        },
        { transaction: t }
      );
    }

    // Commit the transaction before sending notifications
    await t.commit();

    // Send notifications only after successful commit
    if (paymentDetails.status === "captured") {
      try {
        const user = await User.findByPk(notes.userId);

        const smsData = {
          transactionId: razorpay_payment_id,
          paymentAmount: paymentDetails.amount / 100,
          mobile: user.phoneNumber,
          tournamentTitle: notes.tournamentTitle,
        };

        const [sentSms, registerSms] = await Promise.allSettled([
          smsService.sendPaymentConfirmationSMS(smsData),
          smsService.sendRegistrationConfirmationSMS({
            mobile: user.phoneNumber,
            tournamentTitle: tournament.title,
            startDate: tournament.startDate,
          }),
        ]);

        // Create notification records
        const notifications = [];

        if (registerSms.status === "fulfilled") {
          notifications.push({
            userId: notes.userId,
            phoneNumber: user.phoneNumber,
            type: "tournament-registration",
            platform: "sms",
            status: "delivered",
            deliveryAttempts: 1,
            sentAt: new Date(),
            metadata: JSON.stringify(registerSms.value),
          });
        }

        if (sentSms.status === "fulfilled") {
          notifications.push({
            userId: notes.userId,
            phoneNumber: user.phoneNumber,
            type: "payment-confirmation",
            platform: "sms",
            status: "delivered",
            deliveryAttempts: 1,
            sentAt: new Date(),
            metadata: JSON.stringify(sentSms.value),
          });
        }

        if (notifications.length > 0) {
          await Notifications.bulkCreate(notifications);
        }
      } catch (notificationError) {
        console.error("Error sending notifications:", notificationError);
        // Don't fail the entire request for notification errors
      }
    }

    return sendResponse(res, 200, {
      success: true,
      message: "Payment verified successfully",
      data: {
        paymentStatus: payment.paymentStatus,
        transactionId: payment.paymentTransactionId,
        amount: paymentDetails.amount / 100,
        tournamentTitle: notes.tournamentTitle,
      },
    });
  } catch (error) {
    await t.rollback();
    console.error("Error in verifyPayment:", error);
    handleError(res, error);
  }
};

/**
 * Handle Razorpay webhooks for payment status updates
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const handleWebhook = async (req, res) => {
  try {
    const webhookSignature = req.get("X-Razorpay-Signature");
    const webhookBody = JSON.stringify(req.body);

    // Verify webhook signature
    const isValidWebhook = verifyWebhookSignature(
      webhookBody,
      webhookSignature,
      config.razorpay.webhook_secret
    );

    if (!isValidWebhook) {
      console.error("Invalid webhook signature");
      return res.status(400).json({ error: "Invalid webhook signature" });
    }

    const webhookEvent = req.body;
    const processedEvent = processWebhookEvent(webhookEvent);

    if (!processedEvent.success) {
      console.error("Failed to process webhook event:", processedEvent.error);
      return res.status(400).json({ error: "Failed to process webhook" });
    }

    const { event, entity } = processedEvent.processedEvent;

    // Handle different webhook events
    switch (event) {
      case "payment.captured":
        await handlePaymentCaptured(entity);
        break;
      case "payment.failed":
        await handlePaymentFailed(entity);
        break;

      case "payment.authorized":
        await handlePaymentAuthorized(entity);
        break;

      case "order.paid":
        await handleOrderPaid(entity);
        break;

      default:
        break;
    }

    return res.status(200).json({ status: "ok" });
  } catch (error) {
    console.error("Error in handleWebhook:", error);
    return res.status(500).json({ error: "Webhook processing failed" });
  }
};

/**
 * Handle payment captured webhook
 * @param {Object} paymentEntity - Payment entity from webhook
 */
const handlePaymentCaptured = async (paymentEntity) => {
  const t = await sequelize.transaction();

  try {
    const payment = await Payment.findOne({
      where: { razorpayPaymentId: paymentEntity.id },
      transaction: t,
    });

    if (payment && payment.paymentStatus !== "captured") {
      await payment.update(
        {
          paymentStatus: "captured",
          razorpayOrderStatus: "paid",
          paymentRemarks: "Payment captured via webhook",
          razorpayResponse: paymentEntity,
        },
        { transaction: t }
      );
    }

    await t.commit();
  } catch (error) {
    await t.rollback();
    console.error("Error handling payment captured:", error);
  }
};

/**
 * Handle payment authorized webhook
 * @param {Object} paymentEntity - Payment entity from webhook
 */
const handlePaymentAuthorized = async (paymentEntity) => {
  const t = await sequelize.transaction();

  try {
    const payment = await Payment.findOne({
      where: { razorpayPaymentId: paymentEntity.id },
      transaction: t,
    });

    if (payment && payment.paymentStatus !== "authorized") {
      await payment.update(
        {
          paymentStatus: "authorized",
          razorpayOrderStatus: "attempted",
          paymentRemarks: "Payment authorized via webhook",
          razorpayResponse: paymentEntity,
        },
        { transaction: t }
      );
    }

    await t.commit();
  } catch (error) {
    await t.rollback();
    console.error("Error handling payment authorized:", error);
  }
};

/**
 * Handle payment failed webhook
 * @param {Object} paymentEntity - Payment entity from webhook
 */
const handlePaymentFailed = async (paymentEntity) => {
  const t = await sequelize.transaction();

  try {
    const payment = await Payment.findOne({
      where: { razorpayPaymentId: paymentEntity.id },
      transaction: t,
    });

    if (payment && payment.paymentStatus !== "failed") {
      await payment.update(
        {
          paymentStatus: "failed",
          razorpayOrderStatus: "attempted",
          paymentRemarks: `Payment failed: ${
            paymentEntity.error_description || "Unknown error"
          }`,
          razorpayResponse: paymentEntity,
        },
        { transaction: t }
      );
    }

    await t.commit();
  } catch (error) {
    await t.rollback();
    console.error("Error handling payment failed:", error);
  }
};

/**
 * Handle order paid webhook
 * @param {Object} orderEntity - Order entity from webhook
 */
const handleOrderPaid = async (orderEntity) => {
  try {
    const payment = await Payment.findOne({
      where: { razorpayOrderId: orderEntity.id },
    });

    if (payment) {
      await payment.update({
        razorpayOrderStatus: "paid",
        razorpayResponse: orderEntity,
      });
    }
  } catch (error) {
    console.error("Error handling order paid:", error);
  }
};

/**
 * Get payment status by payment ID
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getPaymentStatus = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const userId = req.user.userId;

    const payment = await Payment.findOne({
      where: {
        id: paymentId,
        userId: userId,
      },
      include: [
        {
          model: Tournament,
          attributes: ["title", "startDate"],
        },
        {
          model: Registration,
          attributes: ["regId", "paymentDate"],
        },
      ],
    });

    if (!payment) {
      return sendResponse(res, 404, {
        success: false,
        error: "Payment not found",
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data: {
        paymentId: payment.id,
        status: payment.paymentStatus,
        amount: payment.paymentAmount / 100, // Convert paise to rupees
        transactionId:
          payment.razorpayPaymentId || payment.paymentTransactionId,
        tournamentTitle: payment.Tournament?.title,
        registrationId: payment.Registration?.regId,
        paymentDate: payment.Registration?.paymentDate || payment.paymentDate,
        razorpayOrderStatus: payment.razorpayOrderStatus,
        attempts: payment.razorpayAttempts,
      },
    });
  } catch (error) {
    console.error("Error in getPaymentStatus:", error);
    handleError(res, error);
  }
};

/**
 * Get all payments for a user
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getUserPaymentSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  status: z.enum(["all", "captured", "failed", "pending"]).optional(),
  tournamentTitle: z.string().optional(),
  TransactionId: z.string().optional(),
});
const getUserPayments = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { data, success, errors } = getUserPaymentSchema.safeParse(req.query);

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }
    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: `"Invalid query parameters" ${errors}`,
      });
    }
    const { page, limit, status, tournamentTitle, TransactionId } = data;
    const whareQurey = {};
    if (status && status !== "all") whareQurey.paymentStatus = status;
    if (TransactionId) whareQurey.paymentTransactionId = TransactionId;

    const { rows: payments, count: total } = await Payment.findAndCountAll({
      where: { userId: userId, ...whareQurey },
      attributes: [
        "id",
        "paymentTransactionId",
        "paymentAmount",
        "paymentCurrency",
        "paymentStatus",
        "paymentDate",
        "paymentRemarks",
      ],
      offset: (page - 1) * limit,
      limit,
      include: [
        {
          model: Tournament,
          where: tournamentTitle
            ? { title: { [Op.iLike]: `%${tournamentTitle}%` } }
            : {},
          as: "tournament",
          attributes: ["title"],
        },
      ],
      order: [["paymentDate", "DESC"]],
    });

    if (total === 0) {
      sendResponse(res, 204, {
        success: true,
        data: {
          payments: [],
          total: 0,
          currentPage: page,
          totalPages: 0,
        },
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        payments,
        total,
        currentPage: page,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error in getUserPayments:", error);
    handleError(res, error);
  }
};
const clubPaymentSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  tournamentTitle: z.string().optional(),
  transactionId: z.string().optional(),
  paymentType: z.enum(["player", "club", "all"]).optional().default("all"),
});

const getClubEarnings = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { data, success, errors } = clubPaymentSchema.safeParse(req.query);

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: `Invalid query parameters: ${errors}`,
      });
    }

    const { page, limit, tournamentTitle, transactionId, paymentType } = data;
    const whereQuery = { paymentStatus: "captured" };

    if (transactionId)
      whereQuery.paymentTransactionId = { [Op.iLike]: `%${transactionId}%` };

    // Handle different payment types if specified
    if (paymentType === "player") {
      whereQuery.paymentType = "player";
    } else if (paymentType === "club") {
      whereQuery.paymentType = "club";
    }
    // If no paymentType specified, get both

    // Create a more efficient query by joining first
    const { rows: payments, count: total } = await Payment.findAndCountAll({
      where: whereQuery,
      attributes: [
        "id",
        "paymentTransactionId",
        "paymentAmount",
        "paymentCurrency",
        "paymentStatus",
        "paymentRemarks",
        "userId",
        "paymentType",
        "paymentDate",
        "razorpayResponse", // Added to access payment details
      ],
      offset: (page - 1) * limit,
      limit,
      include: [
        {
          model: Tournament,
          as: "tournament",
          attributes: ["title", "clubId"],
          where: tournamentTitle ? { title: tournamentTitle } : {},
          required: tournamentTitle ? true : false,
        },
        {
          model: User,
          as: "user",
          attributes: ["cbid"],
          include: [
            {
              model: ClubDetail,
              attributes: ["clubId", "clubName"],
              required: false,
            },
          ],
        },
        {
          model: BulkRegistration,
          as: "bulkRegistration",
          attributes: ["playersCount", "totalAmount"],
          required: false,
        },
      ],
      order: [["paymentDate", "DESC"]],
    });

    if (total === 0) {
      return sendResponse(res, 204, {
        success: true,
        data: {
          payments: [],
          total: 0,
          currentPage: page,
          totalPages: 0,
        },
      });
    }

    // Filter payments for the club
    const filteredPayments = payments.filter(
      (payment) => payment.tournament?.clubId === userId
    );

    // Transform the data to include clear payment type info
    const formattedPayments = filteredPayments.map((payment) => {
      const basePayment = {
        id: payment.id,
        paymentTransactionId: payment.paymentTransactionId,
        paymentAmount: payment.paymentAmount,
        paymentCurrency: payment.paymentCurrency,
        paymentStatus: payment.paymentStatus,
        paymentDate: payment.paymentDate,
        paymentRemarks: payment.paymentRemarks,
        tournamentTitle: payment.tournament?.title || "",
        paymentType: payment.paymentType || "player",
        cbid: payment.user?.cbid || null,
      };

      // Extract player information from razorpayResponse if available
      let playerName = null;
      let playerEmail = null;
      let playerPhone = null;

      if (payment.razorpayResponse && payment.razorpayResponse.notes) {
        const notes = payment.razorpayResponse.notes;
        playerName = notes.playerName || notes.clubName || null;
        playerEmail =
          notes.playerEmail ||
          notes.clubEmail ||
          payment.razorpayResponse.email ||
          null;
        playerPhone = notes.playerPhone || notes.clubPhone || null;
      }

      // Add specific details based on payment type
      if (payment.paymentType === "club") {
        return {
          ...basePayment,
          playersCount:
            payment.bulkRegistration?.playersCount ||
            (payment.razorpayResponse?.notes?.playerCount
              ? parseInt(payment.razorpayResponse.notes.playerCount)
              : 0),
          totalAmount:
            payment.bulkRegistration?.totalAmount || payment.paymentAmount,
          registrationType: "Bulk Registration",
          clubId: payment.user?.ClubDetail?.clubId || null,
          clubName:
            payment.user?.ClubDetail?.clubName || playerName || "Unknown Club",
          clubEmail: playerEmail,
          clubPhone: playerPhone,
        };
      } else {
        return {
          ...basePayment,
          playerName: playerName,
          playerEmail: playerEmail,
          playerPhone: playerPhone,
          registrationType: "Player Registration",
        };
      }
    });

    return sendResponse(res, 200, {
      success: true,
      data: {
        payments: formattedPayments,
        total: filteredPayments.length,
        currentPage: page,
        totalPages: Math.ceil(filteredPayments.length / limit),
      },
    });
  } catch (error) {
    console.error("Error in getClubEarnings:", error);
    handleError(res, error);
  }
};
const createBulkPaymentOrder = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { bulkRegistrationId } = req.body;

    if (!userId || !bulkRegistrationId) {
      return sendResponse(res, 400, {
        success: false,
        error: "Missing required parameters",
      });
    }

    // Get bulk registration details
    const bulkRegistration = await BulkRegistration.findOne({
      where: { id: bulkRegistrationId },
    });

    if (!bulkRegistration) {
      return sendResponse(res, 404, {
        success: false,
        error: "Bulk registration not found",
      });
    }

    // Get user details
    const user = await User.findByPk(userId);
    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }

    // Get tournament details
    const tournament = await Tournament.findByPk(bulkRegistration.tournamentId);
    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }

    // Validate bulk registration status
    if (bulkRegistration.registrationStatus !== "pending") {
      return sendResponse(res, 400, {
        success: false,
        error: "Registration is not pending",
      });
    }

    if (
      !bulkRegistration.playerList ||
      bulkRegistration.playerList.length === 0
    ) {
      return sendResponse(res, 400, {
        success: false,
        error: "No players found in bulk registration",
      });
    }

    // Calculate total amount
    const playerCount = bulkRegistration.playerList.length;
    const amount = parseFloat(tournament.entryFee) * playerCount;

    if (isNaN(amount) || amount <= 0) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid tournament entry fee",
      });
    }

    // Generate unique receipt ID
    const receipt = `CB-${uuidv4().substring(0, 8)}`;
    const productinfo = `Bulk Registration for ${tournament.title} (${playerCount} players)`;

    // Create payment record in database
    const payment = await Payment.create({
      userId: userId,
      tournamentId: bulkRegistration.tournamentId,
      bulkRegistrationId: bulkRegistration.id,
      paymentTransactionId: receipt,
      paymentAmount: amount,
      paymentCurrency: "INR",
      paymentType: "club",
      paymentStatus: "pending",
      paymentMethod: "Razorpay",
      paymentRemarks: `Bulk payment for ${tournament.title} - ${playerCount} players`,
      paymentDate: new Date(),
    });

    // Create Razorpay order
    const orderData = {
      amount: amount, // Convert to paise
      currency: "INR",
      receipt: receipt,
      notes: {
        userId: userId.toString(),
        tournamentId: bulkRegistration.tournamentId.toString(),
        bulkRegistrationId: bulkRegistration.id.toString(),
        paymentId: payment.id.toString(),
        tournamentTitle: tournament.title,
        playerCount: playerCount.toString(),
        paymentType: "bulk",
        clubName: user.name,
        clubEmail: user.email,
        clubPhone: user.phoneNumber,
      },
    };

    const orderResult = await createOrder(orderData);

    if (!orderResult.success) {
      // Delete the created payment record if order creation fails
      await Payment.destroy({ where: { id: payment.id } });

      return sendResponse(res, 400, {
        success: false,
        error: "Failed to create payment order",
        details: orderResult.error,
      });
    }

    // Update payment with Razorpay order ID and status
    await payment.update({
      razorpayOrderId: orderResult.order.id,
      razorpayOrderStatus: "created",
      paymentStatus: "created",
    });

    // Generate checkout options for frontend
    const checkoutOptions = {
      key: config.razorpay_key_id,
      order_id: orderResult.order.id,
      amount: orderResult.order.amount, // Amount in paise
      currency: orderResult.order.currency,
      name: "Bulk Tournament Registration",
      description: productinfo,
      image: config.company_logo || "",
      prefill: {
        name: user.name,
        email: user.email,
        contact: user.phoneNumber,
      },
      theme: {
        color: "#3399cc",
      },
      notes: orderResult.order.notes,
    };

    return sendResponse(res, 200, {
      success: true,
      message: "Bulk payment order created successfully",
      data: {
        paymentId: payment.id,
        orderId: orderResult.order.id,
        checkoutOptions,
        tournamentTitle: tournament.title,
        playerCount: playerCount,
        amount: amount, // Return amount in rupees for frontend
      },
    });
  } catch (error) {
    console.error("Error in createBulkPaymentOrder:", error);
    handleError(res, error);
  }
};

/**
 * Verify bulk payment after successful payment on frontend
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const verifyBulkPayment = async (req, res) => {
  const t = await sequelize.transaction();

  try {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
      req.body;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      await t.rollback();
      return sendResponse(res, 400, {
        success: false,
        error: "Missing payment verification parameters",
      });
    }

    // Verify payment signature
    const isValid = verifyPaymentSignature({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
    });

    if (!isValid) {
      await t.rollback();
      console.error("Invalid payment signature");
      return sendResponse(res, 400, {
        success: false,
        error: "Payment verification failed",
        action: "verification_failed",
      });
    }

    // Get payment details from Razorpay
    const paymentDetailsResult = await fetchPaymentDetails(razorpay_payment_id);

    if (!paymentDetailsResult.success) {
      await t.rollback();
      return sendResponse(res, 400, {
        success: false,
        error: "Failed to fetch payment details",
        details: paymentDetailsResult.error,
      });
    }

    const paymentDetails = paymentDetailsResult.payment;
    const { notes } = paymentDetails;

    // Find payment record
    const payment = await Payment.findOne({
      where: {
        razorpayOrderId: razorpay_order_id,
        id: notes.paymentId,
      },
      transaction: t,
    });

    if (!payment) {
      await t.rollback();
      throw new Error("Payment record not found");
    }

    // Map Razorpay status to your enum values
    const getPaymentStatus = (razorpayStatus) => {
      switch (razorpayStatus) {
        case "captured":
          return "captured";
        case "authorized":
          return "authorized";
        case "failed":
          return "failed";
        default:
          return "pending";
      }
    };

    // Update payment record with all Razorpay fields
    await payment.update(
      {
        paymentStatus: getPaymentStatus(paymentDetails.status),
        paymentMethod: paymentDetails.method || "Razorpay",
        paymentReference: razorpay_payment_id,
        razorpayPaymentId: razorpay_payment_id,
        razorpaySignature: razorpay_signature,
        razorpayOrderStatus:
          paymentDetails.status === "captured" ? "paid" : "attempted",
        razorpayAttempts: (payment.razorpayAttempts || 0) + 1,
        paymentRemarks: `Bulk payment ${paymentDetails.status}`,
        razorpayResponse: paymentDetails, // Store complete response
      },
      { transaction: t }
    );

    let tournament, bulkRegistration, newRegistrations;

    if (paymentDetails.status === "captured") {
      // Get tournament details
      tournament = await Tournament.findByPk(notes.tournamentId, {
        include: [
          {
            model: User,
            as: "club",
            attributes: ["name", "phoneNumber"],
          },
        ],
        transaction: t,
      });

      if (!tournament) {
        await t.rollback();
        throw new Error("Tournament not found");
      }

      // Get bulk registration details
      bulkRegistration = await BulkRegistration.findByPk(
        notes.bulkRegistrationId,
        {
          transaction: t,
        }
      );

      if (!bulkRegistration) {
        await t.rollback();
        throw new Error("Bulk registration not found");
      }

      // Update bulk registration status
      await bulkRegistration.update(
        { registrationStatus: "registered" },
        { transaction: t }
      );

      // Count existing registrations for this tournament
      const registrationCount = await Registration.count({
        where: { tournamentId: notes.tournamentId },
        transaction: t,
      });

      const tournamentName = tournament.title;
      const clubName = tournament.club.name;
      const tournamentDate =
        tournament.startDate || tournament.createdAt || new Date();
      const players = bulkRegistration.playerList;

      // Create registrations for all players
      newRegistrations = players.map((player, index) => ({
        playerId: player.playerId,
        genderCategory: player.genderCategory,
        ageCategory: player.ageCategory,
        tournamentTitle: notes.tournamentTitle,
        paymentDate: new Date(),
        tournamentId: notes.tournamentId,
        paymentId: payment.id,
        paymentAmount: tournament.entryFee,
        regId: generateRegistrationId(
          tournamentName,
          clubName,
          tournamentDate,
          registrationCount + index
        ),
      }));

      // Bulk create all registrations
      await Registration.bulkCreate(newRegistrations, { transaction: t });

      // Update payment with registration info
      await payment.update(
        {
          registrationCount: players.length,
        },
        { transaction: t }
      );
    }

    // Commit the transaction before sending notifications
    await t.commit();

    // Send notifications only after successful commit
    if (paymentDetails.status === "captured") {
      try {
        const user = await User.findByPk(notes.userId);

        // Send SMS notifications for bulk registration
        const smsData = {
          transactionId: razorpay_payment_id,
          paymentAmount: paymentDetails.amount / 100,
          mobile: user.phoneNumber,
          tournamentTitle: notes.tournamentTitle,
          playerCount: notes.playerCount,
        };

        const [sentSms, registerSms] = await Promise.allSettled([
          smsService.sendBulkPaymentConfirmationSMS(smsData),
          smsService.sendBulkRegistrationConfirmationSMS({
            mobile: user.phoneNumber,
            tournamentTitle: tournament.title,
            startDate: tournament.startDate,
            playerCount: notes.playerCount,
          }),
        ]);

        // Create notification records
        const notifications = [];

        if (registerSms.status === "fulfilled") {
          notifications.push({
            userId: notes.userId,
            phoneNumber: user.phoneNumber,
            type: "bulk-tournament-registration",
            platform: "sms",
            status: "delivered",
            deliveryAttempts: 1,
            sentAt: new Date(),
            metadata: JSON.stringify(registerSms.value),
          });
        }

        if (sentSms.status === "fulfilled") {
          notifications.push({
            userId: notes.userId,
            phoneNumber: user.phoneNumber,
            type: "bulk-payment-confirmation",
            platform: "sms",
            status: "delivered",
            deliveryAttempts: 1,
            sentAt: new Date(),
            metadata: JSON.stringify(sentSms.value),
          });
        }

        if (notifications.length > 0) {
          await Notifications.bulkCreate(notifications);
        }
      } catch (notificationError) {
        console.error("Error sending notifications:", notificationError);
        // Don't fail the entire request for notification errors
      }
    }

    return sendResponse(res, 200, {
      success: true,
      message: "Bulk payment verified successfully",
      data: {
        paymentStatus: payment.paymentStatus,
        transactionId: payment.paymentTransactionId,
        amount: paymentDetails.amount / 100,
        tournamentTitle: notes.tournamentTitle,
        playerCount: notes.playerCount,
        registrationCount: newRegistrations ? newRegistrations.length : 0,
      },
    });
  } catch (error) {
    await t.rollback();
    console.error("Error in verifyBulkPayment:", error);
    handleError(res, error);
  }
};

/**
 * Handle bulk payment webhooks for Razorpay events
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const handleBulkWebhook = async (req, res) => {
  try {
    const webhookSignature = req.get("X-Razorpay-Signature");
    const webhookBody = JSON.stringify(req.body);

    // Verify webhook signature
    const isValidWebhook = verifyWebhookSignature(
      webhookBody,
      webhookSignature,
      config.razorpay.webhook_secret
    );

    if (!isValidWebhook) {
      console.error("Invalid bulk webhook signature");
      return res.status(400).json({ error: "Invalid webhook signature" });
    }

    const webhookEvent = req.body;
    const processedEvent = processWebhookEvent(webhookEvent);

    if (!processedEvent.success) {
      console.error(
        "Failed to process bulk webhook event:",
        processedEvent.error
      );
      return res.status(400).json({ error: "Failed to process webhook" });
    }

    const { event, entity } = processedEvent.processedEvent;

    // Handle different webhook events for bulk payments
    switch (event) {
      case "payment.captured":
        await handleBulkPaymentCaptured(entity);
        break;

      case "payment.failed":
        await handleBulkPaymentFailed(entity);
        break;

      case "payment.authorized":
        await handleBulkPaymentAuthorized(entity);
        break;

      case "order.paid":
        await handleBulkOrderPaid(entity);
        break;

      default:
        break;
    }

    return res.status(200).json({ status: "ok" });
  } catch (error) {
    console.error("Error in handleBulkWebhook:", error);
    return res.status(500).json({ error: "Bulk webhook processing failed" });
  }
};

/**
 * Handle bulk payment captured webhook
 * @param {Object} paymentEntity - Payment entity from webhook
 */
const handleBulkPaymentCaptured = async (paymentEntity) => {
  const t = await sequelize.transaction();

  try {
    const payment = await Payment.findOne({
      where: {
        razorpayPaymentId: paymentEntity.id,
        paymentType: "club", // Ensure it's a bulk payment
      },
      transaction: t,
    });

    if (payment && payment.paymentStatus !== "captured") {
      // Update payment status
      await payment.update(
        {
          paymentStatus: "captured",
          razorpayOrderStatus: "paid",
          paymentRemarks: "Bulk payment captured via webhook",
          razorpayResponse: paymentEntity,
        },
        { transaction: t }
      );

      // If payment is captured, process bulk registration
      const bulkRegistration = await BulkRegistration.findByPk(
        payment.bulkRegistrationId,
        { transaction: t }
      );

      if (
        bulkRegistration &&
        bulkRegistration.registrationStatus === "pending"
      ) {
        // Update bulk registration status
        await bulkRegistration.update(
          { registrationStatus: "registered" },
          { transaction: t }
        );

        // Get tournament details
        const tournament = await Tournament.findByPk(
          bulkRegistration.tournamentId,
          {
            include: [
              {
                model: User,
                as: "club",
                attributes: ["name"],
              },
            ],
            transaction: t,
          }
        );

        if (tournament) {
          // Count existing registrations
          const registrationCount = await Registration.count({
            where: { tournamentId: bulkRegistration.tournamentId },
            transaction: t,
          });

          const tournamentName = tournament.title;
          const clubName = tournament.club.name;
          const tournamentDate =
            tournament.startDate || tournament.createdAt || new Date();
          const players = bulkRegistration.playerList;

          // Create registrations for all players
          const newRegistrations = players.map((player, index) => ({
            playerId: player.playerId,
            genderCategory: player.genderCategory,
            ageCategory: player.ageCategory,
            tournamentTitle: tournament.title,
            paymentDate: new Date(),
            tournamentId: bulkRegistration.tournamentId,
            paymentId: payment.id,
            paymentAmount: tournament.entryFee,
            regId: generateRegistrationId(
              tournamentName,
              clubName,
              tournamentDate,
              registrationCount + index
            ),
          }));

          // Bulk create all registrations
          await Registration.bulkCreate(newRegistrations, { transaction: t });

          // Commit transaction
          await transaction.commit();
          transaction = null;

          // Send notifications (non-critical, outside transaction)
          try {
            // Create notifications for all players
            await notificationService.createBulkRegistrationNotifications(
              payment.bulkRegistrationId,
              userId
            );
            // Process notifications immediately
            cronService.runJobNow("process-sms-notifications");
          } catch (notificationError) {
            console.error(
              "Error sending notifications (non-critical):",
              notificationError
            );
          }
        }
      }

      await t.commit();
    }
  } catch (error) {
    await t.rollback();
    console.error("Error handling bulk payment captured:", error);
  }
};

/**
 * Handle bulk payment authorized webhook
 * @param {Object} paymentEntity - Payment entity from webhook
 */
const handleBulkPaymentAuthorized = async (paymentEntity) => {
  const t = await sequelize.transaction();

  try {
    const payment = await Payment.findOne({
      where: {
        razorpayPaymentId: paymentEntity.id,
        paymentType: "club", // Ensure it's a bulk payment
      },
      transaction: t,
    });

    if (payment && payment.paymentStatus !== "authorized") {
      await payment.update(
        {
          paymentStatus: "authorized",
          razorpayOrderStatus: "attempted",
          paymentRemarks: "Bulk payment authorized via webhook",
          razorpayResponse: paymentEntity,
        },
        { transaction: t }
      );
    }

    await t.commit();
  } catch (error) {
    await t.rollback();
    console.error("Error handling bulk payment authorized:", error);
  }
};

/**
 * Handle bulk payment failed webhook
 * @param {Object} paymentEntity - Payment entity from webhook
 */
const handleBulkPaymentFailed = async (paymentEntity) => {
  const t = await sequelize.transaction();

  try {
    const payment = await Payment.findOne({
      where: {
        razorpayPaymentId: paymentEntity.id,
        paymentType: "club", // Ensure it's a bulk payment
      },
      transaction: t,
    });

    if (payment && payment.paymentStatus !== "failed") {
      await payment.update(
        {
          paymentStatus: "failed",
          razorpayOrderStatus: "attempted",
          paymentRemarks: `Bulk payment failed: ${
            paymentEntity.error_description || "Unknown error"
          }`,
          razorpayResponse: paymentEntity,
        },
        { transaction: t }
      );
    }

    await t.commit();
  } catch (error) {
    await t.rollback();
    console.error("Error handling bulk payment failed:", error);
  }
};

/**
 * Handle bulk order paid webhook
 * @param {Object} orderEntity - Order entity from webhook
 */
const handleBulkOrderPaid = async (orderEntity) => {
  try {
    const payment = await Payment.findOne({
      where: {
        razorpayOrderId: orderEntity.id,
        paymentType: "club", // Ensure it's a bulk payment
      },
    });

    if (payment) {
      await payment.update({
        razorpayOrderStatus: "paid",
        razorpayResponse: orderEntity,
      });
    }
  } catch (error) {
    console.error("Error handling bulk order paid:", error);
  }
};

// Schema for club payments query parameters
const clubPaymentsSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  status: z.enum(["all", "captured", "pending", "failed"]).default("all"),
  tournamentTitle: z.string().optional(),
  transactionId: z.string().optional(),
});

/**
 * Get club bulk payment registrations with player details
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getClubPayments = async (req, res) => {
  try {
    const userId = req.user.userId;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    // Validate query parameters
    const { data, success, error } = clubPaymentsSchema.safeParse(req.query);

    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: "Invalid query parameters",
        details: error.format(),
      });
    }

    // Use validated parameters
    const { page, limit, status, tournamentTitle, transactionId } = data;

    // Find the club associated with this user
    const club = await ClubDetail.findOne({
      where: { userId },
      attributes: ["id", "clubName"],
    });

    if (!club) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }
    const payementWhereClause = {};
    if (status !== "all") {
      payementWhereClause.paymentStatus = status;
    }
    if (transactionId) {
      payementWhereClause.paymentTransactionId = transactionId;
    }

    // Build the where clause for BulkRegistration
    const whereClause = {
      registeredBy: club.id,
      registrationStatus: "registered",
    };
    // Find bulk registrations with pagination
    const { rows: bulkRegistrations, count: total } =
      await BulkRegistration.findAndCountAll({
        where: whereClause,
        attributes: [
          "id",
          "bulkRegistrationId",
          "registrationStatus",
          "totalAmount",
          "playersCount",
          "createdAt",
        ],
        offset: (page - 1) * limit,
        limit: parseInt(limit),
        include: [
          {
            model: Tournament,
            as: "tournament",
            where: tournamentTitle
              ? { title: { [Op.iLike]: `%${tournamentTitle}%` } }
              : {},
            attributes: ["id", "title", "startDate", "endDate", "entryFee"],
          },
          {
            model: Payment,
            as: "payment",
            where: payementWhereClause,
            attributes: [
              "id",
              "paymentTransactionId",
              "paymentAmount",
              "paymentStatus",
              "paymentMethod",
              "paymentDate",
            ],
          },
        ],
        order: [["createdAt", "DESC"]],
      });

    if (total === 0) {
      return sendResponse(res, 204, {
        success: true,
        data: {
          bulkPayments: [],
          total: 0,
          currentPage: parseInt(page),
          totalPages: 0,
        },
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data: {
        bulkRegistrations,
        total,
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error("Error in getClubPayments:", error);
    handleError(res, error);
  }
};

// Schema for pending bulk registrations query parameters
const pendingBulkRegistrationsSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().default(10),
  tournamentId: z.string().uuid().optional(),
});

/**
 * Get pending bulk registrations for a club that haven't been paid for yet
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getPendingBulkRegistrations = async (req, res) => {
  try {
    const userId = req.user.userId;

    if (!userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "Forbidden",
      });
    }

    // Validate query parameters
    const { data, success, error } = pendingBulkRegistrationsSchema.safeParse(
      req.query
    );

    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: "Invalid query parameters",
        details: error.format(),
      });
    }

    // Use validated parameters
    const { page, limit, tournamentId } = data;

    // Find the club associated with this user
    const club = await ClubDetail.findOne({
      where: { userId },
      attributes: ["id", "clubName"],
    });

    if (!club) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }

    // Build the where clause for BulkRegistration - only pending registrations
    const whereClause = {
      registeredBy: club.id,
      registrationStatus: "pending",
    };

    // Filter by tournament if specified
    if (tournamentId) {
      whereClause.tournamentId = tournamentId;
    }

    // Find pending bulk registrations with pagination
    const { rows: pendingRegistrations, count: total } =
      await BulkRegistration.findAndCountAll({
        where: whereClause,
        attributes: [
          "id",
          "bulkRegistrationId",
          "registrationStatus",
          "totalAmount",
          "playersCount",
          "createdAt",
          "playerList",
        ],
        offset: (page - 1) * limit,
        limit: parseInt(limit),
        include: [
          {
            model: Tournament,
            as: "tournament",
            attributes: [
              "id",
              "title",
              "startDate",
              "endDate",
              "entryFee",
              "registrationDeadline",
            ],
          },
        ],
        order: [["createdAt", "DESC"]],
      });

    if (total === 0) {
      return sendResponse(res, 204, {
        success: true,
        data: {
          pendingRegistrations: [],
          total: 0,
          currentPage: parseInt(page),
          totalPages: 0,
        },
      });
    }

    // Format the response data
    const formattedRegistrations = await Promise.all(
      pendingRegistrations.map(async (registration) => {
        // Get player details for this bulk registration
        const playerIds = registration.playerList.map((p) => p.playerId);

        // Fetch player details
        const players = await User.findAll({
          where: { id: playerIds },
          attributes: ["id", "name", "email", "cbid"],
          include: [
            {
              model: PlayerDetail,
              attributes: [
                "playerTitle",
                "fideRating",
                "fideId",
                "aicfId",
                "gender",
                "dob",
              ],
            },
          ],
        });

        // Enrich player list with user details
        const enrichedPlayerList = registration.playerList.map((player) => {
          const user = players.find((u) => u.id === player.playerId);
          if (!user) return player;

          return {
            ...player,
            cbid: user.cbid,
            email: user.email,
            playerName: user.name || "",
            playerTitle: user.PlayerDetail?.playerTitle || "",
            fideRating: user.PlayerDetail?.fideRating || "",
            fideId: user.PlayerDetail?.fideId || "",
            aicfId: user.PlayerDetail?.aicfId || "",
            gender: user.PlayerDetail?.gender || "",
            dob: user.PlayerDetail?.dob || "",
          };
        });

        // Format the registration data
        return {
          id: registration.id,
          bulkRegistrationId: registration.bulkRegistrationId,
          registrationStatus: registration.registrationStatus,
          totalAmount: registration.totalAmount,
          playersCount: registration.playersCount,
          createdAt: registration.createdAt,
          tournament: registration.tournament
            ? {
                id: registration.tournament.id,
                title: registration.tournament.title,
                startDate: registration.tournament.startDate,
                endDate: registration.tournament.endDate,
                entryFee: registration.tournament.entryFee,
                registrationDeadline:
                  registration.tournament.registrationDeadline,
              }
            : null,
          players: enrichedPlayerList,
        };
      })
    );

    return sendResponse(res, 200, {
      success: true,
      data: {
        pendingRegistrations: formattedRegistrations,
        total,
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error("Error in getPendingBulkRegistrations:", error);
    handleError(res, error);
  }
};
const getPaymentReceipt = async (req, res) => {
  try {
    const { txnId } = req.query;

    if (!txnId) {
      return res.status(400).json({
        success: false,
        error: "Transaction ID is required",
      });
    }

    const raw = await Payment.findOne({
      where: { paymentTransactionId: txnId },
      attributes: [
        "paymentAmount",
        "paymentMethod",
        "paymentTransactionId",
        "paymentDate",
        "paymentStatus",
        "paymentCurrency",
        "paymentType",
        "paymentRemarks",
      ],
      include: [
        {
          model: Tournament,
          as: "tournament",
          attributes: [
            "title",
            "startDate",
            "country",
            "venueAddress",
            "contactPersonName",
            "contactNumber",
            "email",
          ],
        },
        {
          model: User,
          as: "user",
          attributes: ["name", "phoneNumber", "email", "cbid"],
        },
        {
          model: Registration,
          as: "registration",
          attributes: ["regId"],
        },
      ],
      raw: true,
      nest: true,
    });

    if (!raw) {
      return res.status(404).json({
        success: false,
        error: "Payment not found",
      });
    }

    // Format the data for the PDF
    const response = {
      // Payment details
      paymentAmount: raw.paymentAmount
        ? `${raw.paymentCurrency || "INR"} ${raw.paymentAmount}` // Convert from paise to rupees
        : "₹0.00",
      paymentMethod: raw.paymentMethod || "N/A",
      paymentStatus:
        raw.paymentStatus === "captured" ? "paid" : raw.paymentStatus || "N/A",
      paymentTransactionId: raw.paymentTransactionId || txnId,
      paymentDate: raw.paymentDate
        ? new Date(raw.paymentDate).toISOString().split("T")[0]
        : "N/A",
      paymentCurrency: raw.paymentCurrency || "INR",
      paymentType: raw.paymentType || "N/A", // player or club
      paymentRemarks: raw.paymentRemarks || "",

      // Tournament details
      tournamentTitle: raw.tournament?.title || "N/A",
      tournamentStartDate: raw.tournament?.startDate
        ? new Date(raw.tournament.startDate).toISOString().split("T")[0]
        : "N/A",
      tournamentCountry: raw.tournament?.country || "N/A",
      tournamentOrganizerName: raw.tournament?.contactPersonName || "N/A",
      tournamentOrganizerEmail: raw.tournament?.email || "N/A",
      tournamentOrganizerPhoneNumber: raw.tournament?.contactNumber || "N/A",
      tournamentVenueAddress: raw.tournament?.venueAddress || "N/A",

      // Registration details
      registrationId: raw.registration?.regId || "N/A",

      // Player/User details
      playerId: raw.user?.cbid || "N/A",
      playerName: raw.user?.name || "N/A",
      playerPhoneNumber: raw.user?.phoneNumber || "N/A",
      playerEmail: raw.user?.email || "N/A",

      // Metadata
      timestamp: new Date().toISOString(),
    };

    return sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    console.error("Error processing receipt request:", error);
    return handleError(res, error);
  }
};

module.exports = {
  createPaymentOrder,
  verifyPayment,
  handleWebhook,
  getPaymentStatus,
  getUserPayments,
  getClubEarnings,
  createBulkPaymentOrder,
  verifyBulkPayment,
  handleBulkWebhook,
  getClubPayments,
  getPendingBulkRegistrations,
  getPaymentReceipt,
};

function generateRegistrationId(
  tournamentName,
  clubName,
  tournamentDate,
  currentCount = 0
) {
  // Generate club abbreviation (3 chars)
  const clubAbbr = generateAbbreviation(clubName, 3);

  // Generate tournament abbreviation (3 chars)
  const tournamentAbbr = generateAbbreviation(tournamentName, 3);

  // Extract year and month from tournament date
  const date = new Date(tournamentDate);
  const year = date.getFullYear().toString().slice(2); // Get last 2 digits of year
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Month is 0-indexed

  // Format counter with leading zeros (4 digits)
  const paddedCount = String(currentCount + 1).padStart(4, "0");

  // Combine into the final format with date info
  return `${clubAbbr}-${tournamentAbbr}-${month}${year}-${paddedCount}`;
}

/**
 * Generate an abbreviation from a name with specified length
 *
 * @param name The name to abbreviate
 * @param length The desired abbreviation length
 * @returns An abbreviation in lowercase
 */
function generateAbbreviation(name, length) {
  // Normalize the name: convert to lowercase and remove special characters
  const normalized = name.toLowerCase().replace(/[^\w\s]/g, "");

  // Split into words
  const words = normalized.split(/\s+/).filter((w) => w.length > 0);

  // If we have enough words, use first letter of each word
  if (words.length >= length) {
    return words
      .slice(0, length)
      .map((w) => w[0])
      .join("");
  }

  // If we don't have enough words, use first letters plus more letters from first word
  let abbr = words.map((w) => w[0]).join("");

  // If still not enough, add more characters from the first word
  if (abbr.length < length && words.length > 0) {
    const firstWord = words[0];
    abbr += firstWord.substring(1, length - abbr.length + 1);
  }

  // Pad with 'x' if necessary (should rarely happen)
  while (abbr.length < length) {
    abbr += "x";
  }

  return abbr;
}
