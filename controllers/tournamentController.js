// const  Tournament  = require("../models/tournament");
const {
  Tournament,
  User,
  PlayerDetail,
  Registration,
  InviteRequest,
  Ranking,
  Certificate,
} = require("../config/db").models;
const { Op, Sequelize } = require("sequelize");
const { literal } = require("sequelize");
const {
  createTournamentSchema,
  getAllTournamentSchema,
  updateTournamentSchema,
} = require("../schema/tournamentSchema");

const { sendResponse, handleError } = require("../utils/apiResponse");
const { CheckEligibleCategory } = require("../utils/elegibleCategory");
const { deleteFromS3, generatePresignedUrl } = require("../utils/s3");
const emailService = require("../utils/mailer/emailService");
const { config } = require("../config/config");

/**
 * Get a tournament by ID
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getSingleTournament = async (req, res) => {
  try {
    const title = req.params.id;

    if (typeof title !== "string" || title === undefined) {
      sendResponse(res, 400, {
        success: false,
        error: "Invalid tournament ID",
      });
      return;
    }
    const newTitle = decodeURIComponent(title);

    const tournament = await Tournament.findOne({
      where: { title: newTitle, tournamentStatus: { [Op.ne]: "archived" } },
      attributes: {
        exclude: ["createdAt", "updatedAt"],
      },
      include: [
        {
          model: User,
          as: "arbiter",
          attributes: ["name", "id"],
          required: false,
        },
      ],
    });
    if (!tournament) {
      sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
      return;
    }

    sendResponse(res, 200, {
      success: true,
      data: tournament,
    });
    return;
  } catch (error) {
    handleError(res, error);
    return;
  }
};

// /**
//  * Delete a tournament by ID
//  * @param {import('express').Request} req - Express request object
//  * @param {import('express').Response} res - Express response object
//  */
//  const deleteTournament = async (req, res) => {
//   try {
//     const id = Number(req.params.id);
//     const result = tournamentIdSchema.safeParse({id});
//     console.log(result.error);
//     if (!result.success) {
//       sendResponse(res, 400, {
//         success: false,
//         error: "Invalid tournament ID",
//       });
//       return;
//     }

//     const deletedCount = await Tournament.destroy({
//       where: { id: result.data.id },
//     });

//     if (deletedCount === 0) {
//       sendResponse(res, 404, {
//         success: false,
//         error: "Tournament not found",
//       });
//       return;
//     }

//     sendResponse(res, 200, {
//       success: true,
//       message: "Tournament deleted successfully",
//     });
//     return;
//   } catch (error) {
//     handleError(res, error);
//     return;
//   }
// };

/**
 * Update a tournament by ID
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const editTournament = async (req, res) => {
  try {
    const id = req.params.id;
    const userId = req.user.userId;
    const bodyResult = updateTournamentSchema.safeParse(req.body);

    if (!id || !bodyResult.success) {
      sendResponse(res, 422, {
        success: false,
        error: "Validation failed",
        data: {
          idErrors: !id ? "Invalid tournament ID" : undefined,
          bodyErrors: !bodyResult.success
            ? bodyResult.error.format()
            : undefined,
        },
      });
      return;
    }

    const { title, chiefArbiterName, ...updateData } = bodyResult.data;
    const formattedTitle = title?.toLowerCase().replace(/\s+/g, "-");

    // First, check if the tournament exists and get its current state
    const tournament = await Tournament.findOne({
      where: {
        id: id,
        tournamentStatus: { [Op.ne]: "archived" },
      },
    });

    if (!tournament) {
      sendResponse(res, 404, {
        success: false,
        error: { message: "Tournament not found or is archived" },
      });
      return;
    }

    // Check if the tournament already has an arbiter
    if (tournament.arbiterId && tournament.arbiterId !== chiefArbiterName.id) {
      sendResponse(res, 409, {
        success: false,
        error: { message: "Tournament already has an arbiter" },
      });
      return;
    }

    // Determine the brochure URL to use
    let brochureUrl = tournament?.brochureUrl; // Keep existing by default
    let shouldDeleteOldBrochure = false;

    // Only update brochure URL if a new file was uploaded
    if (req?.file?.location) {
      brochureUrl = req.file.location;
      // Only delete old brochure if we're replacing it with a new one
      shouldDeleteOldBrochure =
        tournament?.brochureUrl && tournament.brochureUrl !== brochureUrl;
    }
    // Prepare update data - only include brochureUrl if it's being changed
    const updatePayload = {
      title: formattedTitle || tournament.title,
      ...updateData,
    };
    // Only include brochureUrl in update if a new file was uploaded
    if (req?.file?.location) {
      updatePayload.brochureUrl = brochureUrl;
    }
    // Update the tournament
    const [updatedCount, updatedTournaments] = await Tournament.update(
      updatePayload,
      {
        where: { id: id, tournamentStatus: { [Op.ne]: "archived" } },
        returning: true,
      }
    );

    if (shouldDeleteOldBrochure) {
      try {
        await deleteFromS3(tournament.brochureUrl);
      } catch (error) {
        console.error("Failed to delete old brochure from S3:", error);
        // Don't fail the entire operation if S3 deletion fails
      }
    }
    if (updatedCount === 0) {
      sendResponse(res, 404, {
        success: false,
        error: { message: "Tournament not updated" },
      });
      return;
    }

    // Get the updated tournament (first element of the returned array)
    const updatedTournament = updatedTournaments[0];

    // Try to create a notification if chief arbiter information is provided
    if (
      chiefArbiterName &&
      typeof chiefArbiterName === "object" &&
      chiefArbiterName.name &&
      chiefArbiterName.id &&
      typeof chiefArbiterName.id === "string" &&
      chiefArbiterName.id.trim() !== ""
    ) {
      // Check for existing notification
      const existingInviteRequest = await InviteRequest.findOne({
        where: {
          type: "arbiter-request",
          [Op.and]: [
            Sequelize.literal(
              `metadata->'tournament'->>'tournamentId' = '${updatedTournament.id}'`
            ),
            Sequelize.literal(`metadata->'club'->>'clubId' = '${userId}'`),
          ],
        },
      });

      // Only create notification if one doesn't already exist
      if (!existingInviteRequest) {
        try {
          const arbiterId = chiefArbiterName.id.trim();

          // Extra validation to ensure we have a valid arbiter ID
          if (arbiterId) {
            await InviteRequest.create({
              userId: arbiterId,
              type: "arbiter-request",
              title: "Tournament Chief Arbiter Request",
              message: `You have been requested to join as chief arbiter for the tournament "${id}"`,
              metadata: {
                arbiter: {
                  arbiterName: chiefArbiterName.name,
                  arbiterId: arbiterId,
                },
                tournament: {
                  tournamentName: id,
                  tournamentId: updatedTournament.id,
                },
                club: {
                  clubId: userId,
                },
              },
            });
            emailService.sendArbiterTournamentInviteEmail({
              email: chiefArbiterName.email,
              subject: `Invitation to arbiter ${updatedTournament.title}`,
              message: `You have been requested to join as chief arbiter for the tournament "${updatedTournament.title}"`,
              arbiterName: chiefArbiterName.name,
              tournamentName: updatedTournament.title,
              clubName: updatedTournament.clubName,
              tournamentDates: `${updatedTournament.startDate} to ${updatedTournament.endDate}`,
              venue: updatedTournament.venue,
              inviteUrl: `${config.frontend_url}/dashboard`,
            });
          } else {
            console.error("Invalid arbiter ID provided");
          }
        } catch (error) {
          console.error("Error creating arbiter request:", error);
          // We don't want to fail the entire tournament update if just the notification fails
        }
      }
    }

    sendResponse(res, 200, {
      success: true,
      error: { message: "Tournament updated successfully" },
    });
  } catch (error) {
    console.error("Error updating tournament:", error);
    handleError(res, error);
  }
};

/**
 * Create a new tournament
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const createTournament = async (req, res) => {
  try {
    const result = createTournamentSchema.safeParse(req.body);
    const userId = req.user.userId;

    if (!result.success) {
      sendResponse(res, 422, {
        success: false,
        error: "Validation failed",
        data: result.error.format(),
      });
      return;
    }
    const { title, chiefArbiterName, ...data } = result.data;
    const formattedTitle = title.toLowerCase().replace(/\s+/g, "-");

    const existingTournament = await Tournament.findOne({
      where: {
        title: formattedTitle,
      },
    });

    if (existingTournament) {
      sendResponse(res, 409, {
        success: false,
        error: {
          message:
            " Tournament already exists or conflicts with existing data. Please check your tournament details",
        },
      });
      return;
    }
    const brochureUrl = req?.file?.location;
    const tournament = await Tournament.create({
      clubId: userId,
      title: formattedTitle,
      brochureUrl,
      ...data,
    });

    if (!tournament) {
      sendResponse(res, 500, {
        success: false,
        error: "Failed to create tournament",
      });
      return;
    }

    // Check if we have valid arbiter information to create a notification
    if (
      chiefArbiterName &&
      typeof chiefArbiterName === "object" &&
      chiefArbiterName.name &&
      chiefArbiterName.id &&
      typeof chiefArbiterName.id === "string" &&
      chiefArbiterName.id.trim() !== ""
    ) {
      const arbiterId = chiefArbiterName.id.trim();

      if (arbiterId) {
        await InviteRequest.create({
          userId: arbiterId,
          type: "arbiter-request",
          title: "Tournament Chief Arbiter Request",
          message: `You have been requested to join as chief arbiter for the tournament "${tournament.title}"`,
          metadata: {
            arbiter: {
              arbiterName: chiefArbiterName.name,
              arbiterId: arbiterId,
            },
            tournament: {
              tournamentName: tournament.title,
              tournamentId: tournament.id,
            },
            club: {
              clubId: userId,
            },
          },
        });
        emailService.sendArbiterTournamentInviteEmail({
          email: chiefArbiterName.email,
          subject: `Invitation to arbiter ${tournament.title}`,
          message: `You have been requested to join as chief arbiter for the tournament "${tournament.title}"`,
          arbiterName: chiefArbiterName.name,
          tournamentName: tournament.title,
          clubName: tournament.clubName,
          tournamentDates: `${tournament.startDate} to ${tournament.endDate}`,
          venue: tournament.venue,
          inviteUrl: `${config.frontend_url}/dashboard`,
        });
      } else {
        deleteFromS3(brochureUrl);
        await tournament.destroy();
        return sendResponse(res, 400, {
          success: false,
          error: "Invalid arbiter ID provided",
        });
      }
    }

    sendResponse(res, 201, {
      success: true,
      message: "Tournament created successfully",
      data: tournament,
    });
  } catch (error) {
    console.error("Error creating tournament:", error);
    deleteFromS3(req?.file?.location);
    sendResponse(res, 500, {
      success: false,
      error:
        "An error occurred while creating the tournament. Please try again.",
    });
  }
};
/**
 * Get all tournaments with pagination and filtering
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getAllTournament = async (req, res) => {
  try {
    const result = getAllTournamentSchema.safeParse(req.query);
    if (!result.success) {
      sendResponse(res, 422, {
        success: false,
        error: "Invalid query parameters",
        data: result.error.format(),
      });
      return;
    }

    const {
      page,
      limit,
      title,
      city,
      age,
      tournamentType,
      tournamentCategory,
      country,
      state,
      district,
      month,
      year,
    } = result.data;
    const offset = (page - 1) * limit;

    const whereClause = {};

    const searchTitle = title?.trim().toLowerCase().replace(/\s+/g, "-");
    if (title) whereClause.title = { [Op.iLike]: `%${searchTitle}%` };
    if (city) whereClause.city = { [Op.iLike]: `%${city}%` };
    if (tournamentType)
      whereClause.fideRated = tournamentType === "rated" ? true : false;
    if (tournamentCategory) whereClause.tournamentCategory = tournamentCategory;
    if (country) whereClause.country = { [Op.iLike]: `%${country}%` };
    if (state) whereClause.state = { [Op.iLike]: `%${state}%` };
    if (district) whereClause.state = { [Op.iLike]: `%${district}%` };

    if (month) {
      whereClause[Op.and] = [
        ...(whereClause[Op.and] || []),
        literal(`EXTRACT(MONTH FROM "start_date") = ${month}`),
        literal(`EXTRACT(MONTH FROM "registration_start_date") = ${month}`),
      ];
    }

    if (year) {
      whereClause[Op.and] = [
        ...(whereClause[Op.and] || []),
        literal(`EXTRACT(YEAR FROM "start_date") = ${year}`),
        literal(`EXTRACT(YEAR FROM "registration_start_date") = ${year}`),
      ];
    }

    const { rows: tournaments, count: total } =
      await Tournament.findAndCountAll({
        where: {
          ...whereClause,
          tournamentStatus: { [Op.ne]: "archived" },
        },
        order: [["createdAt", "DESC"]],
        offset,
        limit,
      });

    if (total === 0) {
      sendResponse(res, 204, {
        success: true,
        error: "No tournaments found",
        data: { total: 0, tournaments: [], currentPage: page, totalPages: 0 },
      });
      return;
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        tournaments,
        total,
        currentPage: page,
        totalPages: Math.ceil(total / limit),
      },
    });
    return;
  } catch (error) {
    handleError(res, error);
    return;
  }
};
const getUpcomingTournament = async (req, res) => {
  const UserId = req.user.userId;
  const { page, limit = 3 } = req.query;
  try {
    const whereClause = {
      [Op.or]: [
        { startDate: { [Op.gte]: new Date() } },
        { endDate: { [Op.gte]: new Date() } },
        { registrationEndDate: { [Op.gte]: new Date() } },
        { registrationStartDate: { [Op.gte]: new Date() } },
      ],
    };
    whereClause.clubId = UserId;
    const offset = (page - 1) * limit;
    const { rows: tournaments, count: total } =
      await Tournament.findAndCountAll({
        attributes: ["title"],
        where: {
          ...whereClause,
          tournamentStatus: { [Op.ne]: "archived" },
        },
        order: [["createdAt", "DESC"]],
        offset,
        limit,
      });
    if (total === 0) {
      sendResponse(res, 404, {
        success: false,
        error: "No tournaments found",
        data: { total: 0, currentPage: page, totalPages: 0, tournaments: [] },
      });
      return;
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        tournaments,
        total,
        currentPage: page,
        totalPages: Math.ceil(total / limit),
      },
    });
    return;
  } catch (error) {
    handleError(res, error);
    return;
  }
};
const deleteTournament = async (req, res) => {
  const UserId = req.user.userId;
  try {
    const { id } = req.params;
    const tournament = await Tournament.findOne({
      where: { clubId: UserId, id: id },
    });

    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found or you don't have permission to delete it",
      });
    }
    if (tournament.tournamentStatus === "active") {
      return sendResponse(res, 400, {
        success: false,
        error: "Tournament is active, cannot delete",
      });
    }
    if (tournament.tournamentStatus === "completed") {
      return sendResponse(res, 400, {
        success: false,
        error: "Tournament is completed, cannot delete",
      });
    }
    tournament.tournamentStatus = "archived";
    await tournament.save();

    sendResponse(res, 200, {
      success: true,
      message: "Tournament deleted successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};
const getClubTournaments = async (req, res) => {
  try {
    const UserId = req.user.userId;
    const { page, limit = 3, title, status } = req.query;
    const offset = (page - 1) * limit;
    const whareClause = {
      clubId: UserId,
      tournamentStatus: { [Op.ne]: "archived" },
    };

    const searchTitle = title?.trim().toLowerCase().replace(/\s+/g, "-");
    if (title) whareClause.title = { [Op.iLike]: `%${searchTitle}%` };
    if (status) {
      if (status === "upcoming") {
        whareClause.startDate = { [Op.gt]: new Date() };
      } else if (status === "completed") {
        whareClause.endDate = { [Op.lt]: new Date() };
      } else if (status === "in-progress") {
        whareClause.startDate = { [Op.lte]: new Date() };
        whareClause.endDate = { [Op.gte]: new Date() };
      } else {
        whareClause.tournamentStatus = status;
      }
    }

    const { rows: tournaments, count: total } =
      await Tournament.findAndCountAll({
        where: whareClause,
        attributes: [
          "title",
          "startDate",
          "endDate",
          "registrationEndDate",
          "city",
          "tournamentStatus",
        ],
        offset,
        limit,
      });
    sendResponse(res, 200, {
      success: true,
      data: {
        tournaments,
        total,
        currentPage: page,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

const downlaodBrochure = async (req, res) => {
  try {
    const { id } = req.params;
    const tournament = await Tournament.findOne({
      where: { id: id },
      attributes: ["brochureUrl"],
    });
    if (!tournament) {
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }
    const brochureUrl = tournament.brochureUrl;
    const presignedUrl = await generatePresignedUrl(brochureUrl);

    return sendResponse(res, 200, { success: true, data: presignedUrl });
  } catch (error) {
    console.error("Error generating pre-signed URL:", error);
    handleError(res, error);
  }
};
const updateTournamentRounds = async (req, res) => {
  try {
    const { id } = req.params;
    const { maleAgeCategory, femaleAgeCategory } = req.body;
    const tournamentId = decodeURIComponent(id);

    const tournament = await Tournament.findOne({
      where: { title: tournamentId },
    });

    if (!tournament) {
      return res.status(404).json({
        success: false,
        error: "Tournament not found",
      });
    }

    // Validate that categories exist in tournament
    const validateCategories = (rounds, availableCategories, gender) => {
      Object.keys(rounds).forEach((category) => {
        if (!availableCategories.includes(category)) {
          throw new Error(
            `${gender} category '${category}' not available in tournament`
          );
        }
      });
    };

    if (maleAgeCategory && Object.keys(maleAgeCategory).length > 0) {
      validateCategories(maleAgeCategory, tournament.maleAgeCategory, "Male");
    }

    if (femaleAgeCategory && Object.keys(femaleAgeCategory).length > 0) {
      validateCategories(
        femaleAgeCategory,
        tournament.femaleAgeCategory,
        "Female"
      );
    }

    // Update the rounds configuration
    tournament.numberOfRounds = {
      maleAgeCategory: maleAgeCategory || {},
      femaleAgeCategory: femaleAgeCategory || {},
    };

    await tournament.save();

    sendResponse(res, 200, {
      success: true,
      message: "Tournament rounds updated successfully",
      data: {
        id: tournament.id,
        numberOfRounds: tournament.numberOfRounds,
      },
    });
  } catch (error) {
    console.error("Error updating tournament rounds:", error);
    handleError(res, error);
  }
};

const updateTournamentCertificateData = async (req, res) => {
  try {
    const { id } = req.params;
    const { certificateData, templateId, tournamentTitle } = req.body;
    const tournamentId = decodeURIComponent(id);

    const tournament = await Tournament.findOne({
      where: { title: tournamentId },
    });

    if (!tournament) {
      return res.status(404).json({
        success: false,
        error: "Tournament not found",
      });
    }

    const certificateConfig = {
      organization: certificateData?.organization || "",
      poweredBy: certificateData?.poweredBy || "",
      sponsorName: certificateData?.sponsorName || "",
      subtitle: certificateData?.subtitle || "",
      tournamentDate: certificateData?.tournamentDate || "",
      venue: certificateData?.venue || "",
      templateId: templateId || 1,
      tournamentTitle: certificateData?.tournamentTitle || "",
    };

    tournament.certificateData = certificateConfig;
    await tournament.save();

    sendResponse(res, 200, {
      success: true,
      message: "Tournament certificate data updated successfully",
      data: {
        id: tournament.id,
        certificateData: tournament.certificateData,
      },
    });
  } catch (error) {
    console.error("Error updating tournament certificate data:", error);
    handleError(res, error);
  }
};

const { v4: uuidv4 } = require("uuid");

const generateCertificate = async (req, res) => {
  try {
    const tournamentTitle = decodeURIComponent(req.params.id);

    const tournament = await Tournament.findOne({
      where: { title: tournamentTitle },
    });

    if (!tournament) {
      return res.status(404).json({
        success: false,
        error: "Tournament not found",
      });
    }
    if (!tournament.certificateData) {
      return res.status(400).json({
        success: false,
        error: "create certificate data first",
      });
    }

    const leaderBoard = await Ranking.findAll({
      where: { tournament_id: tournament.id },
      attributes: [
        "player_name",
        "total_points",
        "rank",
        "age_category",
        "gender_category",
        "round_id",
      ],
      order: [["rank", "ASC"]],
    });

    if (!leaderBoard || leaderBoard.length === 0) {
      return res.status(404).json({
        success: false,
        error: "Tournament not finished yet",
      });
    }

    const {
      certificateData: {
        templateId,
        tournamentTitle: certTitle,
        organization,
        poweredBy,
        sponsorName,
        subtitle,
        tournamentDate,
        venue,
      },
    } = tournament;

    const metadata = {
      organization,
      poweredBy,
      sponsorName,
      subtitle,
      tournamentDate,
      venue,
    };

    // Helper function to create short tournament name
    const createShortTournamentName = (title) => {
      return title
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase())
        .join("")
        .slice(0, 6); // Limit to 6 characters
    };

    const shortTournamentName = createShortTournamentName(tournament.title);

    // Generate unique certificate IDs for each certificate
    const certificates = await Certificate.bulkCreate(
      leaderBoard.map((entry, index) => {
        // Create unique certificate ID combining tournament info and player details
        const certificateId = `${uuidv4()}-${shortTournamentName}-${
          entry.rank
        }`;

        // Alternative: Using UUID for more randomness
        // const certificateId = `${tournament.id}-${uuidv4()}`;

        return {
          certificateId,
          tournamentId: tournament.id,
          playerName: entry.player_name,
          totalPoints: entry.total_points,
          rank: entry.rank,
          ageCategory: entry.age_category,
          genderCategory: entry.gender_category,
          roundId: entry.round_id,
          templateId,
          tournamentTitle: certTitle,
          ...metadata,
          metadata: {
            ...metadata,
            certificateId: certificateId,
          },
        };
      })
    );

    return res.status(200).json({
      success: true,
      message: "Certificates generated successfully",
      data: certificates,
    });
  } catch (error) {
    console.error("Error generating certificate:", error);
    return handleError(res, error);
  }
};

module.exports = {
  createTournament,
  editTournament,
  getAllTournament,
  getSingleTournament,
  getUpcomingTournament,
  deleteTournament,
  getClubTournaments,
  downlaodBrochure,
  updateTournamentRounds,
  updateTournamentCertificateData,
  generateCertificate,
};
