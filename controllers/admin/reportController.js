const { getAllArbiterSchema, getAllPlayerSchema } = require("../../schema/playerSchama");
const { sendResponse, handleError } = require("../../utils/apiResponse");
const { Op, Sequelize, where } = require("sequelize");
const { v4: UUIDV4 } = require("uuid");
const {
  PlayerDetail,
  ArbiterDetails,
  ClubDetail,
  Pairing,
  User,
  Otp,
  Tournament,
  InviteRequest,
  Notifications,
} = require("../../config/db").models;
const {
  arbiterDetailSchema,
  updateDetailsSchema,
} = require("../../schema/arbiterSchema");

const { sequelize } = require("../../config/db");
const { deleteFromS3 } = require("../../utils/s3");
const { config } = require("../../config/config");
const user = require("../../models/user");
const notificationService = require("../../services/notificationService");
const cronService = require("../../services/cronService");
const { exportToExcel, exportToPDF } = require("../../utils/report-generation");
const { getAllClubSchema } = require("../../schema/clubSchema");

const getAllArbiter = async (req, res) => {
  const { data, success, error } = getAllArbiterSchema.safeParse(req.query);

  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });      
  }
  try {
    const {
      page = 1,
      limit = 10,
      city,
      arbiterId,
      arbiterName,
      country,
      state,
      district,
    } = data;
    const offset = (page - 1) * limit;
    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (arbiterId && !arbiterId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${arbiterId}%` } },
        { aicfId: { [Op.iLike]: `%${arbiterId}%` } },
        { stateId: { [Op.iLike]: `%${arbiterId}%` } },
        { districtId: { [Op.iLike]: `%${arbiterId}%` } },
        { officialId: { [Op.iLike]: `%${arbiterId}%` } },
      ];
    }
    let userWhereClause = {};
    if (arbiterId && arbiterId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${arbiterId}%` } };
    }
    if (arbiterName) userWhereClause.name = { [Op.iLike]: `%${arbiterName}%` };
    const { rows: arbiters, count } = await ArbiterDetails.findAndCountAll({
      where: whereClause,
      offset,
      limit,
      attributes: [
        "title",

        "fideId",
        "aicfId",
        "districtId",
        "stateId",
        "profileUrl",
        "country",
        "state",
        "district",
        "city",
      ],
      include: [
        {
          model: User,
          where: userWhereClause,
          as: "user",
          attributes: ["cbid", "name"],
          required: true,
        },
      ],
    });

    const formattedArbiters = arbiters.map((arbiter) => {
      const arbiterData = arbiter.toJSON();
      if (arbiter.user) {
        return { ...arbiterData, ...arbiter.user.toJSON() };
      }
      return arbiterData;
    });
    const response = {
      arbiters: formattedArbiters,
      total: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit),
    };
    sendResponse(res, 200, {
      success: true,
      data: response,
    });
  } catch (error) {
    handleError(res, error);
  }
};
const reportGenerate = async (req, res) => {
  try {
    const { type } = req.query;
    // const { data, success, error } = getAllArbiterSchema.safeParse(req.query);

//   if (!success) {
//     return sendResponse(res, 422, {
//       success: false,
//       error: "Invalid query parameters",
//     });      
//   }
    const {
      city,
      arbiterId,
      arbiterName,
      country,
      state,
      district,
    } = req.query;
    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (arbiterId && !arbiterId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${arbiterId}%` } },
        { aicfId: { [Op.iLike]: `%${arbiterId}%` } },
        { stateId: { [Op.iLike]: `%${arbiterId}%` } },
        { districtId: { [Op.iLike]: `%${arbiterId}%` } },
        { officialId: { [Op.iLike]: `%${arbiterId}%` } },
      ];
    }
    let userWhereClause = {};
    if (arbiterId && arbiterId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${arbiterId}%` } };
    }
    if (arbiterName) userWhereClause.name = { [Op.iLike]: `%${arbiterName}%` };
    const { rows: details } = await ArbiterDetails.findAndCountAll({
        where:whereClause,
       attributes: {
        exclude: ["createdAt", "updatedAt", "id", "profileUrl", "userId"],
      },
      include: [
        {
          model: User,
          as: "user",
          where:userWhereClause,
          attributes: ["id", "name", "email", "phoneNumber"],
          required: false,
        },
      ],
    });

    if (!details || details.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter not found",
      });
    }
    console.log("details",details)
    // return;
    const plainDetails = details.map((arbiter) => {
      const plain = arbiter.toJSON();
      const flattened = {
        ...plain,
        userName: plain.user?.name || "",
        userEmail: plain.user?.email || "",
        userPhone: plain.user?.phoneNumber || "",
      };
      delete flattened.user;
      return flattened;
    });

    // console.log("plainDetails",plainDetails)

    // return;

    // const newArray = Array.isArray(details)?details:[details]
    let result;
    if (type !== "pdf") {
      result = await exportToExcel({
        data: plainDetails,
        sheetName: "Arbiter_details",
        title: "Arbiter Details Report",
        reportType: "Registered Arbiters",
      });

      if (!result.success) {
        return sendResponse(res, 500, {
          success: false,
          error: result.error,
        });
      }

      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=arbiter_details_report.xlsx"
      );
    } else {
      result = await exportToPDF({
        data: plainDetails,
        title: "Arbiter Details Report",
      });
      if (!result.success) {
        return sendResponse(res, 500, {
          success: false,
          error: result.error,
        });
      }

      res.set({
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename=Arbiter_Details.pdf`,
      });
    }

    return res.send(result.buffer);
  } catch (e) {
    handleError(res, e);
    return;
  }
};

const PublicPlayerReport = async (req, res) => {

  const { data, success, error } = getAllPlayerSchema.safeParse(req.query);
  if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }
  try {
    const {
      city,
      playerId,
      playerName,
      country,
      state,
      district,
    } = data;
    const whereClause = {};
    const filterFields = { city, country, state, district };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    if (playerId && !playerId.toLowerCase().startsWith("cb")) {
      whereClause[Op.or] = [
        { fideId: { [Op.iLike]: `%${playerId}%` } },
        { aicfId: { [Op.iLike]: `%${playerId}%` } },
        { stateId: { [Op.iLike]: `%${playerId}%` } },
        { districtId: { [Op.iLike]: `%${playerId}%` } },
      ];
    }
    let userWhereClause = {};
    if (playerId && playerId.toLowerCase().startsWith("cb")) {
      userWhereClause = { cbid: { [Op.iLike]: `%${playerId}%` } };
    }
    if (playerName) userWhereClause.name = { [Op.iLike]: `%${playerName}%` };

    const { rows: details } = await PlayerDetail.findAndCountAll({
      where: whereClause,
      attributes: {
        exclude: ['createdAt', 'updatedAt', 'id', 'profileUrl', 'userId', 'clubId', 'termsAndConditions']
      },
      include: [
        {
          model: User,
          attributes: ['id', 'name', 'email', 'phoneNumber'],
          required: false,
        }
      ],
    });

    if (!details || details.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }
    // console.log("details",details)
    // return;
    const plainDetails = details.map(player => {
      const plain = player.toJSON();
      const flattened = {
        ...plain,
        playerName: plain.User?.name || '',
        playerEmail: plain.User?.email || '',
        playerPhone: plain.User?.phoneNumber || '',
      };
      delete flattened.User;
      return flattened;
    });

    const result = await exportToExcel({
      data: plainDetails,
      sheetName: 'Player_Public_details',
      title: 'Player List Report',
      reportType: 'Registered Players'
    })

    if (!result.success) {
      return sendResponse(res, 500, {
        success: false,
        error: result.error,
      });
    }

    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=player_list_report${playerName ? `_${playerName.replace(/\s+/g, '_')}` : ''}.xlsx`
    );

    return res.send(result.buffer)

  } catch (e) {
    handleError(res, e);
    return;
  }
}
const ClubReportGenerate = async (req, res) => {
  
  const { data, success, error } = getAllClubSchema.safeParse(req.query);
    if (!success) {
    return sendResponse(res, 422, {
      success: false,
      error: "Invalid query parameters",
    });
  }

  try {

    const {
      clubName = "",
      country = "",
      state = "",
      district = "",
      city = "",
    } = data;

    const whereClause = {};
    const filterFields = { clubName, country, state, district, city };
    Object.entries(filterFields).forEach(([key, value]) => {
      if (value) whereClause[key] = { [Op.iLike]: `%${value}%` };
    });
    const { type } = req.query;
    const { rows: details } = await ClubDetail.findAndCountAll({
      where:whereClause,
      attributes: {
        exclude: [
          "createdAt",
          "updatedAt",
          "id",
          "profileUrl",
          "userId",
          "clubId",
        ],
      },
      include: [
        {
          model: User,
          as: "user",
          attributes: ["id", "name", "email", "phoneNumber"],
          required: false,
        },
      ],
    });

    if (!details || details.length === 0) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }
    // console.log("details",details)
    // return;
    const plainDetails = details.map((club) => {
      const plain = club.toJSON();
      const flattened = {
        ...plain,
        userName: plain.user?.name || "",
        userEmail: plain.user?.email || "",
        userPhone: plain.user?.phoneNumber || "",
      };
      delete flattened.user;
      return flattened;
    });

    // console.log("plainDetails",plainDetails)

    // return;

    // const newArray = Array.isArray(details)?details:[details]
    let result;
    if (type !== "pdf") {
      result = await exportToExcel({
        data: plainDetails,
        sheetName: "Club_details",
        title: "Club Details Report",
        reportType: "Registered Clubs",
      });

      if (!result.success) {
        return sendResponse(res, 500, {
          success: false,
          error: result.error,
        });
      }

      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        "attachment; filename=club_details_report.xlsx"
      );
    } else {
      result = await exportToPDF({
        data: plainDetails,
        title: "Club Details Report",
      });
      if (!result.success) {
        return sendResponse(res, 500, {
          success: false,
          error: result.error,
        });
      }

      res.set({
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename=Club_Details.pdf`,
      });
    }

    return res.send(result.buffer);
  } catch (e) {
    handleError(res, e);
    return;
  }
};

module.exports = {
  reportGenerate,
  PublicPlayerReport,
  ClubReportGenerate
};

