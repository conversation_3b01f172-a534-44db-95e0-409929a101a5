const { getAllArbiterSchema, getAllPlayerSchema } = require("../../schema/playerSchama");
const { sendResponse, handleError } = require("../../utils/apiResponse");
const { Op, Sequelize, where } = require("sequelize");
const { v4: UUIDV4 } = require("uuid");
const {
  PlayerDetail,
  ArbiterDetails,
  ClubDetail,
  User,
  Tournament,
  Payment,
} = require("../../config/db").models;
const {
  arbiterDetailSchema,
  updateDetailsSchema,
} = require("../../schema/arbiterSchema");

const { sequelize } = require("../../config/db");
const { deleteFromS3 } = require("../../utils/s3");
const { config } = require("../../config/config");
const notificationService = require("../../services/notificationService");
const cronService = require("../../services/cronService");
const { exportToExcel, exportToPDF } = require("../../utils/report-generation");
const { getAllClubSchema } = require("../../schema/clubSchema");

const getSinglePlayer = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid player ID",
      });
    }
    const player = await User.scope('adminAccess').findOne({
      where: { cbid: id },
      attributes: ["name", "cbid", "id", "email", "phoneNumber", "isActive"],
      include: [
        {
          model: PlayerDetail,
          attributes: [
            "dob",
            "gender",
            "alternateContact",
            "parentGuardianName",
            "emergencyContact",
            "playerTitle",
            "clubId",
            "fideRating",
            "fideId",
            "aicfId",
            "stateId",
            "districtId",
            "association",
            "club",
            "country",
            "state",
            "profileUrl",
            "district",
            "city",
            "address",
            "pincode",
          ],
        },
      ],
    });

    if (!player) {
      return sendResponse(res, 404, {
        success: false,
        error: "Player not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: player,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getClubDetailById = async (req, res) => {
  try {
    const AdminScopedUser = User.scope('adminAccess');
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid club ID",
      });
    }
    const clubDetail = await ClubDetail.findOne({
      where: { clubId: id },
      attributes: {
        exclude: ["id", "userId", "createdAt", "updatedAt"],
      },
      include: [{
        model: AdminScopedUser,
        as: "user",
        attributes: ["name", "cbid", "id", "email", "phoneNumber", "isActive"]
      }]
    });

    if (clubDetail) {
      const {
        clubName,
        clubDistrictId,
        clubId,
        country,
        state,
        district,
        city,
        address,
        locationUrl,
        profileUrl,
        contactPersonName,
        contactPersonNumber,
        contactPersonEmail,
        alternateContactNumber,
        user,
      } = clubDetail.toJSON();
      const filteredClubDetail = {
        clubName,
        clubDistrictId,
        country,
        state,
        clubId,
        district,
        city,
        address,
        locationUrl,
        profileUrl,
        contactPersonName,
        contactPersonNumber,
        contactPersonEmail,
        alternateContactNumber,
        id: user?.id,
        name: user?.name,
        email: user?.email,
        cbid: user?.cbid,
        phoneNumber: user?.phoneNumber,
        IsActive: user?.isActive,
      };
      clubDetail.dataValues = filteredClubDetail;
    }

    if (!clubDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club detail not found",
      });
    }

    // Add additional information or related data if needed
    // For example, you could include the number of members or recent tournaments

    return sendResponse(res, 200, {
      success: true,
      data: clubDetail,
    });
  } catch (error) {
    console.error("Error in getClubDetailById:", error);
    handleError(res, error);
  }
};


const getSingleArbiter = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || typeof id !== "string") {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid arbiter ID",
      });
    }

    const arbiter = await User.scope('adminAccess').findOne({
      where: { cbid: id },
      attributes: ["name", "cbid", "id", "email", "phoneNumber", "isActive"],
      include: [
        {
          model: ArbiterDetails,
          attributes: [
            "title",
            "fideId",
            "aicfId",
            "districtId",
            "profileUrl",
            "alternateContact",
            "officialId",

            "stateId",
            "country",
            "state",
            "district",
            "city",
          ],
          required: true,
        },
      ],
    });
    if (!arbiter) {
      return sendResponse(res, 404, {
        success: false,
        error: "Arbiter not found",
      });
    }

    sendResponse(res, 200, {
      success: true,
      data: arbiter,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getPayment = async (req, res) => {
  try {
    // Step 1: Fetch tournaments
    let tournament, count;
    try {
      const result = await Tournament.findAndCountAll();
      tournament = result.rows;
      count = result.count;
    } catch (err) {
      console.error("Error fetching tournaments:", err);
      return sendResponse(res, 500, {
        success: false,
        message: "Failed to fetch tournament data.",
      });
    }

    // Step 2: Fetch payments
    let payments;
    try {
      payments = await Payment.findAll({
        attributes: ['id', 'tournamentId', 'paymentAmount']
      });
    } catch (err) {
      console.error("Error fetching payments:", err);
      return sendResponse(res, 500, {
        success: false,
        message: "Failed to fetch payment data.",
      });
    }

    // Step 3: Calculate total amount
    let totalAmount = 0;
    try {
      totalAmount = payments.reduce((sum, p) => {
        const amount = Number(p.paymentAmount);
        if (isNaN(amount)) throw new Error(`Invalid paymentAmount: ${p.paymentAmount}`);
        return sum + amount;
      }, 0);
    } catch (err) {
      console.error("Error calculating total amount:", err);
      return sendResponse(res, 500, {
        success: false,
        message: "Failed to calculate total payment amount.",
      });
    }

    // Step 4: Calculate earnings
    const totalEarnings = Math.ceil(totalAmount / 10);

    // Step 5: Send successful response
    sendResponse(res, 200, {
      success: true,
      data: {
        tournamentCount: count,
        totalAmount,
        totalEarnings,
      },
    });

  } catch (error) {
    console.error("Unexpected error in getPayment:", error);
    handleError(res, error); // generic fallback
  }
};

const getAllTournament = async (req, res) => {
  try {
    // Get pagination params from query string, not params
    let { page, limit = 10, tournamentName, date } = req.query;

    // Convert to numbers and validate
    page = parseInt(page);
    limit = parseInt(limit, 10);

    if (isNaN(page) || isNaN(limit) || page < 1 || limit < 1) {
      return sendResponse(res, 400, {
        success: false,
        message: "Invalid 'page' or 'limit' parameters. Both must be positive integers.",
      });
    }

    const whereClause = {};
    
    if (tournamentName) {
      whereClause.title = { [Op.iLike]: `%${tournamentName}%` };
    }

    const dateRange = getDateRange(date);
    if (dateRange) {
      whereClause.startDate = { [Op.between]: dateRange };
    }

    // Optional: Set maximum limit to prevent abuse
    if (limit > 100) {
      limit = 100;
    }

    const offset = (page - 1) * limit;

    const { rows: tournamentData, count } = await Tournament.findAndCountAll({
      where: whereClause,
      attributes: [
        'id', 
        'title',
        // Add virtual field to calculate total payment amount
        [
          sequelize.literal(`(
            SELECT COALESCE(SUM(CAST("payments"."payment_amount" AS DECIMAL)), 0)
            FROM "payments" AS "payments"
            WHERE "payments"."tournament_id" = "Tournament"."id"
          )`),
          'totalPaymentAmount'
        ]
      ],
      include: {
        model: Payment,
        as: 'payments',
        attributes: ['id', 'tournamentId', 'paymentAmount'],
      },
      limit,
      distinct: true,
      offset,
    });

    // Alternative approach: Calculate total payment amount after fetching data
    const tournamentDataWithTotals = tournamentData.map(tournament => {
      const tournamentObj = tournament.toJSON();
      
      // Calculate total payment amount from included payments
      const totalPaymentAmount = tournamentObj.payments.reduce((sum, payment) => {
        return sum + (parseFloat(payment.paymentAmount) || 0);
      }, 0);
      
      return {
        ...tournamentObj,
        totalPaymentAmount: totalPaymentAmount
      };
    });

    // Handle case where no tournaments exist
    if (count === 0) {
      return sendResponse(res, 200, {
        success: true,
        data: [], 
        count: 0,
        page,
        totalPages: 0,
        message: "No tournaments found."
      });
    }
    
    const totalPages = Math.ceil(count / limit);

    // Check if requested page exceeds available pages
    if (page > totalPages) {
      return sendResponse(res, 400, {
        success: false,
        message: `Page ${page} does not exist. Total pages: ${totalPages}`,
      });
    }

    // Success response
    sendResponse(res, 200, {
      success: true,
      data: tournamentDataWithTotals,
      pagination: {
        currentPage: page,
        totalPages,
        totalRecords: tournamentData.length,
        recordsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });

  } catch (error) {
    console.error("Unexpected error in getAllTournament:", error);
    return sendResponse(res, 500, {
      success: false,
      message: "An unexpected error occurred while fetching tournaments.",
    });
  }
};

module.exports = {
  getSinglePlayer,
  getClubDetailById,
  getSingleArbiter,
  getPayment,
  getAllTournament,
};

const getDateRange = (dateFilter) => {
  const today = new Date();
  
  switch (dateFilter) {
    case '7':
      const past7Days = new Date();
      past7Days.setDate(today.getDate() - 7);
      return [past7Days, today];
    
    case '30':
      const past30Days = new Date();
      past30Days.setDate(today.getDate() - 30);
      return [past30Days, today];
    
    case 'all':
    default:
      return null; // No date filter
  }
};