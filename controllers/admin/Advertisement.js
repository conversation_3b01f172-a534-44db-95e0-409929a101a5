const { Advertisement } = require("../../config/db").models;
const { sendResponse, handleError } = require("../../utils/apiResponse");
const { deleteFromS3 } = require("../../utils/s3");

// Create Advertisement
const createAdvertisement = async (req, res) => {
    try {
        const { Url, country, city, state, fileFormat, userType, name } = req.body;

        if (!userType) {
            return sendResponse(res, 400, {
                success: false,
                message: "Missing required field: userType",
            });
        }

        if (!req.file && !Url) {
            return sendResponse(res, 400, {
                success: false,
                message: "Missing required field: either image URL or file",
            });
        }

        if (!name) {
            return sendResponse(res, 400, {
                success: false,
                message: "Missing required field:name",
            });
        }


        const count = await Advertisement.count({ where: { userType, isActive: true } });
        const isActive = count === 0;
        console.log("isActive", isActive)
        const url = req.file?.location || Url;

        const newAd = await Advertisement.create({
            url,
            isActive,
            userType,
            country,
            city,
            state,
            format: fileFormat,
            name
        });

        return sendResponse(res, 201, {
            success: true,
            data: newAd,
            message: "Advertisement created successfully",
        });
    } catch (error) {
        return handleError(res, error);
    }
};

// Read All Advertisements
const getAllAdvertisements = async (req, res) => {
    try {
        // Parse and validate query parameters
        let page = parseInt(req.query.page, 10) || 1;
        let limit = parseInt(req.query.limit, 10) || 10;

        if (page < 1) page = 1;
        if (limit < 1) limit = 10;

        const offset = (page - 1) * limit;

        // Fetch data with count
        const { rows: ads, count } = await Advertisement.findAndCountAll({ offset, limit });
        const totalPages = Math.ceil(count / limit)
        return sendResponse(res, 200, {
            success: true,
            data: ads,
            pagination: { count, totalPages }
        });
    } catch (error) {
        return handleError(res, error);
    }
};

// Read Single Advertisement
const getAdvertisementById = async (req, res) => {
    try {
        const { id } = req.params;
        const ad = await Advertisement.findOne({ where: { id: id } });

        if (!ad) {
            return sendResponse(res, 404, {
                success: false,
                error: "Advertisement not found",
            });
        }

        return sendResponse(res, 200, {
            success: true,
            data: ad,
        });
    } catch (error) {
        return handleError(res, error);
    }
};

// Update Advertisement
const updateAdvertisement = async (req, res) => {
    try {
        const { id } = req.params;
        const { title, description, imageUrl, startDate, endDate } = req.body;

        const [updatedCount] = await Advertisement.update(
            { title, description, imageUrl, startDate, endDate },
            { where: { id } }
        );

        if (updatedCount === 0) {
            return sendResponse(res, 404, {
                success: false,
                error: "Advertisement not found or no changes made",
            });
        }

        return sendResponse(res, 200, {
            success: true,
            message: "Advertisement updated successfully",
        });
    } catch (error) {
        return handleError(res, error);
    }
};

// Delete Advertisement
const deleteAdvertisement = async (req, res) => {
    try {
        const { id } = req.params;

        const find = await Advertisement.findOne({ where: { id } });
        if (!find) {
            return sendResponse(res, 404, {
                success: false,
                error: "Advertisement not found",
            });
        }

        if (find.isActive) {
            return sendResponse(res, 409, {
                success: false,
                error: "You need to deactivate the advertisement before deleting it.",
            });
        }
        if(find.name !== 'youtube-video' ){
        deleteFromS3(find.url);}

        const deleted = await Advertisement.destroy({ where: { id } });

        if (!deleted) {
            return sendResponse(res, 404, {
                success: false,
                error: "Advertisement not found",
            });
        }

        return sendResponse(res, 200, {
            success: true,
            message: "Advertisement deleted successfully",
        });
    } catch (error) {
        return handleError(res, error);
    }
};

// Update Advertisement Active/Inactive
const updateIsActive = async (req, res) => {
    try {
        const { id, isActive, userType } = req.body;

        // Check for existing active ad for same userType, excluding current id
        if (isActive === true) {
            const activeAds = await Advertisement.findAll({
                where: {
                    userType,
                    isActive: true,
                }
            });

            if (activeAds.length > 0) {
                return sendResponse(res, 401, {
                    success: false,
                    error: `An active advertisement already exists for '${userType}'. Please deactivate it first.`,
                });
            }
        }

        const [updatedCount] = await Advertisement.update(
            { isActive },
            { where: { id } }
        );

        if (updatedCount === 0) {
            return sendResponse(res, 404, {
                success: false,
                error: "Advertisement not found",
            });
        }

        return sendResponse(res, 200, {
            success: true,
            message: `Advertisement has been ${isActive ? 'activated' : 'deactivated'} successfully.`,
        });
    } catch (error) {
        return handleError(res, error);
    }
};
module.exports = {
    createAdvertisement,
    getAllAdvertisements,
    getAdvertisementById,
    updateAdvertisement,
    deleteAdvertisement,
    updateIsActive,
};