const { Content } = require("../../config/db").models;
const { sendResponse, handleError } = require("../../utils/apiResponse");

const getContent = async (req, res) => {
  try {
    const { slug } = req.params;
    const content = await Content.findOne({
      where: { slug },
    });
    if (!content) {
      return sendResponse(res, 204, {
        success: false,
        error: "Content not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: content?.htmlContent,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const updateContent = async (req, res) => {
  try {
    const { slug } = req.params;
    const { htmlContent } = req.body;
    const content = await Content.findOne({
      where: { slug },
    });
    if (!content) {
      return sendResponse(res, 404, {
        success: false,
        error: "Content not found",
      });
    }
    await content.update({ htmlContent });
    sendResponse(res, 200, {
      success: true,
      data: content,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const createContent = async (req, res) => {
  try {
    const { slug, htmlContent } = req.body;
    const existingContent = await Content.findOne({
      where: { slug },
    });
    if (existingContent) {
      return sendResponse(res, 409, {
        success: false,
        error: "Content already exists",
      });
    }
    const content = await Content.create({
      slug,
      htmlContent,
    });
    sendResponse(res, 201, {
      success: true,
      data: content,
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  getContent,
  updateContent,
  createContent,
};
