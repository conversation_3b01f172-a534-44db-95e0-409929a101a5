const { v4: uuidv4 } = require("uuid");
const { config } = require("../config/config");
const { sendResponse, handleError } = require("../utils/apiResponse");

const { Refund, Payment, Registration, User, Tournament, Notifications } =
  require("../config/db").models;
const {
  createRefund,
  verifyWebhookSignature,
  processWebhookEvent,
  logTransaction,
} = require("../utils/razorPay");
const emailService = require("../utils/mailer/emailService");
const { sequelize } = require("../config/db");
const { z } = require("zod");
const { Op } = require("sequelize");
const smsService = require("../utils/sms/smsService");

/**
 * Initiate a refund for a payment
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const initiateRefund = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const userId = req.user.userId;
    const { tournamentId, reason, comments } = req.body;

    if (!tournamentId || !reason) {
      await transaction.rollback();
      return sendResponse(res, 400, {
        success: false,
        error: "Reason is required",
      });
    }

    // Find the tournament
    const tournament = await Tournament.findOne({
      where: { id: tournamentId },
      transaction,
    });

    if (!tournament) {
      await transaction.rollback();
      return sendResponse(res, 404, {
        success: false,
        error: "Tournament not found",
      });
    }

    // Check if tournament has already started
    const currentDate = new Date();
    if (new Date(tournament.startDate) <= currentDate) {
      await transaction.rollback();
      return sendResponse(res, 400, {
        success: false,
        error:
          "Cannot cancel registration for a tournament that has already started",
      });
    }

    // Check if player is registered for the tournament
    const registration = await Registration.findOne({
      where: {
        playerId: userId,
        tournamentId: tournament.id,
      },
      include: [
        {
          model: Payment,
          as: "payment",
          required: true,
          where: { paymentStatus: "captured" },
        },
      ],
      transaction,
    });

    if (!registration) {
      await transaction.rollback();
      return sendResponse(res, 404, {
        success: false,
        error:
          "You are not registered for this tournament or payment is not completed",
      });
    }

    // Check if refund already exists for this payment
    const payment = registration.payment;
    const existingRefund = await Refund.findOne({
      where: { paymentId: payment.id },
      transaction,
    });

    if (existingRefund) {
      await transaction.rollback();
      return sendResponse(res, 400, {
        success: false,
        error: "A refund for this registration already exists",
        data: { refundStatus: existingRefund.refundStatus },
      });
    }

    const razorpayPaymentId = payment.razorpayPaymentId;

    if (!razorpayPaymentId) {
      await transaction.rollback();
      return sendResponse(res, 400, {
        success: false,
        error: "Payment does not have a valid Razorpay transaction ID",
      });
    }

    // Calculate refund amount based on cancellation time
    let refundAmount = payment.paymentAmount;
    let refundType = "full";

    // Generate a unique refund reference ID
    const refundReference = `RF-${uuidv4().substring(0, 8)}`;

    const refundRemarks = `Player cancelled registration. Reason: ${reason}. ${
      comments ? `Comments: ${comments}` : ""
    }`;

    // Create refund record in database
    const refund = await Refund.create(
      {
        paymentId: payment.id,
        tournamentId: tournament.id,
        userId: userId,
        razorpayPaymentId,
        refundReference,
        refundAmount,
        refundCurrency: payment.paymentCurrency || "INR",
        refundStatus: "pending",
        refundRemarks: refundRemarks,
        refundType,
        refundReason: "player_cancelled",
        refundInitiatedBy: userId,
        refundSpeed: "normal",
      },
      { transaction }
    );

    const i = await registration.update(
      { status: "cancelled" },
      { transaction: transaction }
    );
    console.log(i)
    // Commit the transaction before making external API call
    await transaction.commit();

    // Process the refund with Razorpay
    try {
      const refundResponse = await createRefund(razorpayPaymentId, {
        amount: refundAmount,
        notes: {
          tournament_id: tournament.id,
          user_id: userId,
          refund_reference: refundReference,
          reason: "player_cancelled",
        },
        receipt: refundReference,
        speed: "normal",
      });
      console.log(refundResponse)

      // Update refund record with Razorpay response
      if (refundResponse.success) {
        await refund.update({
          refundStatus: "processing",
          razorpayRefundId: refundResponse.refund.id,
          razorpayResponse: refundResponse.refund,
        });
      } else {
        await refund.update({
          refundStatus: "failed",
          refundRemarks: `Failed to process refund: ${refundResponse.error}`,
          razorpayResponse: refundResponse.details,
        });

        return sendResponse(res, 400, {
          success: false,
          error: "Failed to process refund with Razorpay",
          message: refundResponse.error,
        });
      }

      // Get user details for notifications
      const user = await User.findOne({ where: { id: userId } });

      // Send notification to user
      try {
        // Send email notification
        emailService.sendRefundInitiatedEmail(user, tournament, refund);

        // Send SMS notification
        smsService.sendRefundInitiatedSMS({
          mobile: user.phoneNumber,
          refundAmount,
          tournamentTitle: tournament.title,
        });
      } catch (notificationError) {
        console.error("Error sending refund notification:", notificationError);
        // Continue with response even if notification fails
      }

      return sendResponse(res, 200, {
        success: true,
        message: "Registration cancelled and refund initiated successfully",
        data: {
          refundId: refund.id,
          refundReference,
          razorpayRefundId: refundResponse.refund.id,
          refundStatus: refund.refundStatus,
          refundAmount: refundResponse.refund.formattedAmount,
        },
      });
    } catch (refundError) {
      console.error("Error processing refund with Razorpay:", refundError);

      // Update refund record to failed status
      await refund.update({
        refundStatus: "failed",
        refundRemarks: `Failed to process refund: ${refundError.message}`,
      });

      return sendResponse(res, 500, {
        success: false,
        error: "Failed to process refund with payment gateway",
        message: refundError.message,
      });
    }
  } catch (error) {
    console.error("Error in cancelRegistration:", error);
    if (transaction) await transaction.rollback();
    handleError(res, error);
  }
};

/**
 * Get refund status
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getRefundStatus = async (req, res) => {
  try {
    const { refundId } = req.params;
    const userId = req.user.userId;
    const isAdmin = req.user.role === "admin";

    if (!refundId) {
      return sendResponse(res, 400, {
        success: false,
        error: "Refund ID is required",
      });
    }

    const refund = await Refund.findOne({
      where: { id: refundId },
      include: [
        {
          model: Payment,
          as: "payment",
        },
        {
          model: Tournament,
          as: "tournament",
        },
      ],
    });

    if (!refund) {
      return sendResponse(res, 404, {
        success: false,
        error: "Refund not found",
      });
    }

    // Check if user is authorized to view refund status
    const isClubOwner = refund.tournament.clubId === userId;
    if (!isAdmin && !isClubOwner && refund.userId !== userId) {
      return sendResponse(res, 403, {
        success: false,
        error: "You are not authorized to view this refund",
      });
    }

    // If refund is in processing state, check status from PayU
    if (refund.refundStatus === "processing" && refund.refundTransactionId) {
      try {
        const statusResponse = await checkRefundStatus(
          refund.refundTransactionId
        );

        // Update refund status based on PayU response
        if (statusResponse.status === "success") {
          const refundStatus =
            statusResponse.msg === "Refund Completed Successfully"
              ? "completed"
              : "processing";

          await refund.update({
            refundStatus,
            refundResponse: {
              ...refund.refundResponse,
              statusCheck: statusResponse,
            },
            refundDate:
              refundStatus === "completed" ? new Date() : refund.refundDate,
          });

          // If refund is now completed, send notification
          if (
            refundStatus === "completed" &&
            refund.refundStatus !== "completed"
          ) {
            try {
              // Send email notification
              await emailService.sendRefundCompletedEmail(
                refund.user,
                refund.tournament,
                refund
              );

              // Send SMS notification
              await smsService.sendRefundCompletedSMS({
                mobile: refund.user.phoneNumber,
                refundAmount: refund.refundAmount,
                tournamentTitle: refund.tournament.title,
              });
            } catch (notificationError) {
              console.error(
                "Error sending refund completion notification:",
                notificationError
              );
            }
          }
        }
      } catch (statusError) {
        console.error("Error checking refund status with PayU:", statusError);
        // Continue with response even if status check fails
      }
    }

    return sendResponse(res, 200, {
      success: true,
      data: refund,
    });
  } catch (error) {
    console.error("Error in getRefundStatus:", error);
    handleError(res, error);
  }
};

/**
 * Handle Razorpay refund webhook callback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const handleRefundWebhook = async (req, res) => {
  const startTime = Date.now();

  try {
    const webhookSignature = req.get("X-Razorpay-Signature");
    const webhookBody = JSON.stringify(req.body);

    // Verify webhook signature
    const isValidWebhook = verifyWebhookSignature(
      webhookBody,
      webhookSignature,
      config.razorpay.webhook_secret
    );

    if (!isValidWebhook) {
      console.error("❌ Invalid refund webhook signature");
      return res.status(400).json({
        success: false,
        error: "Invalid webhook signature",
      });
    }

    const webhookEvent = req.body;
    const processedEvent = processWebhookEvent(webhookEvent);

    if (!processedEvent.success) {
      console.error(
        "❌ Failed to process refund webhook event:",
        processedEvent.error
      );
      return res.status(400).json({
        success: false,
        error: "Failed to process webhook",
      });
    }

    const { event, entity } = processedEvent.processedEvent;

    // Handle different refund webhook events
    switch (event) {
      case "refund.created":
        await handleRefundCreated(entity);
        break;
      case "refund.processed":
        await handleRefundProcessed(entity);
        break;
      case "refund.failed":
        await handleRefundFailed(entity);
        break;
      case "refund.speed_changed":
        await handleRefundSpeedChanged(entity);
        break;
      default:
        break;
    }

    // Log transaction for audit purposes
    logTransaction(
      {
        event,
        entity,
        processingTime: Date.now() - startTime,
      },
      "refund_webhook"
    );

    return res.status(200).json({
      success: true,
      message: "Refund webhook processed successfully",
    });
  } catch (error) {
    console.error("❌ Error in handleRefundWebhook:", error);

    // Log error for debugging
    logTransaction(
      {
        error: error.message,
        stack: error.stack,
        webhookBody: req.body,
        processingTime: Date.now() - startTime,
      },
      "refund_webhook_error"
    );

    return res.status(500).json({
      success: false,
      error: "Refund webhook processing failed",
    });
  }
};

/**
 * Handle refund created webhook (when refund is initiated)
 * @param {Object} refundEntity - Refund entity from webhook
 */
const handleRefundCreated = async (refundEntity) => {
  const transaction = await sequelize.transaction();

  try {
    const refund = await Refund.findOne({
      where: { razorpayRefundId: refundEntity.id },
      transaction,
    });

    if (refund && refund.refundStatus !== "processing") {
      await refund.update(
        {
          refundStatus: "processing",
          refundRemarks: "Refund initiated by Razorpay",
          razorpayResponse: refundEntity,
          refundInitiatedAt: new Date(),
        },
        { transaction }
      );
    }

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    console.error("❌ Error handling refund created:", error);
    throw error;
  }
};

/**
 * Handle refund processed webhook (when refund is completed successfully)
 * @param {Object} refundEntity - Refund entity from webhook
 */
const handleRefundProcessed = async (refundEntity) => {
  const transaction = await sequelize.transaction();

  try {
    const refund = await Refund.findOne({
      where: { razorpayPaymentId: refundEntity.id },
      include: [
        {
          model: Payment,
          as: "payment",
          include: [{ model: User, as: "user" }],
        },
      ],
      transaction,
    });

    if (!refund) {
      console.error(`❌ Refund record not found for ID: ${refundEntity.id}`);
      await transaction.rollback();
      return;
    }

    // Update refund status to completed
    await refund.update(
      {
        refundStatus: "processed",
        refundResponse: refundEntity,
        refundCompletedAt: new Date(),
        refundRemarks: "Refund processed successfully by Razorpay",
      },
      { transaction }
    );

    // Update related payment status
    if (refund.payment) {
      await refund.payment.update(
        {
          paymentStatus: "refunded",
        },
        { transaction }
      );
    }

    // Update registration status if exists
    const registration = await Registration.findOne({
      where: {
        paymentId: refund.paymentId,
        tournamentId: refund.tournamentId,
      },
      transaction,
    });

    if (registration) {
      await registration.update(
        {
          status: "refunded",
        },
        { transaction }
      );
    }

    // Get tournament details for notifications
    const tournament = await Tournament.findByPk(refund.tournamentId, {
      include: [
        {
          model: User,
          as: "club",
          attributes: ["name"],
        },
      ],
      transaction,
    });

    await transaction.commit();

    // Send success notifications (outside transaction)
    if (refund.payment?.user && tournament) {
      await sendRefundSuccessNotifications(
        refund.payment.user,
        tournament,
        refund
      );
    }
  } catch (error) {
    await transaction.rollback();
    console.error("❌ Error handling refund processed:", error);
    throw error;
  }
};

/**
 * Handle refund failed webhook
 * @param {Object} refundEntity - Refund entity from webhook
 */
const handleRefundFailed = async (refundEntity) => {
  const transaction = await sequelize.transaction();

  try {
    const refund = await Refund.findOne({
      where: { razorpayRefundId: refundEntity.id },
      include: [
        {
          model: Payment,
          as: "payment",
          include: [{ model: User, as: "user" }],
        },
      ],
      transaction,
    });

    if (!refund) {
      console.error(`❌ Refund record not found for ID: ${refundEntity.id}`);
      await transaction.rollback();
      return;
    }

    // Update refund status to failed
    await refund.update(
      {
        refundStatus: "failed",
        refundResponse: refundEntity,
        refundRemarks: `Refund failed: ${
          refundEntity.error_description || "Unknown error"
        }`,
        errorCode: refundEntity.error_code || null,
      },
      { transaction }
    );

    // Get tournament details for notifications
    const tournament = await Tournament.findByPk(refund.tournamentId, {
      transaction,
    });

    await transaction.commit();

    // Send failure notifications (outside transaction)
    if (refund.payment?.user && tournament) {
      await sendRefundFailureNotifications(
        refund.payment.user,
        tournament,
        refund,
        refundEntity.error_description
      );
    }
  } catch (error) {
    await transaction.rollback();
    console.error("❌ Error handling refund failed:", error);
    throw error;
  }
};

/**
 * Handle refund speed changed webhook
 * @param {Object} refundEntity - Refund entity from webhook
 */
const handleRefundSpeedChanged = async (refundEntity) => {
  const transaction = await sequelize.transaction();

  try {
    const refund = await Refund.findOne({
      where: { razorpayRefundId: refundEntity.id },
      transaction,
    });

    if (refund) {
      await refund.update(
        {
          refundRemarks: `Refund speed changed to: ${refundEntity.speed_processed}`,
          razorpayResponse: refundEntity,
        },
        { transaction }
      );
    }

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    console.error("❌ Error handling refund speed changed:", error);
    throw error;
  }
};

/**
 * Send refund success notifications
 * @param {Object} user - User object
 * @param {Object} tournament - Tournament object
 * @param {Object} refund - Refund object
 */
const sendRefundSuccessNotifications = async (user, tournament, refund) => {
  try {
    // Send email notification
    if (emailService.sendRefundCompletedEmail) {
      await emailService.sendRefundCompletedEmail(user, tournament, refund);
    }

    // Send SMS notification
    if (smsService.sendRefundCompletedSMS && user.phoneNumber) {
      const smsData = {
        mobile: user.phoneNumber,
        refundAmount: refund.refundAmount,
        tournamentTitle: tournament.title,
        refundId: refund.refundTransactionId,
        transactionId: refund.razorpayRefundId,
      };

      const refundSms = await smsService.sendRefundCompletedSMS(smsData);

      // Create notification record
      await Notifications.create({
        userId: refund.userId,
        phoneNumber: user.phoneNumber,
        type: "refund-completed",
        platform: "sms",
        status: "delivered",
        deliveryAttempts: 1,
        sentAt: new Date(),
        metadata: JSON.stringify(refundSms),
      });
    }
  } catch (error) {
    console.error("❌ Error sending refund success notifications:", error);
    // Don't throw error to avoid affecting webhook response
  }
};

/**
 * Send refund failure notifications
 * @param {Object} user - User object
 * @param {Object} tournament - Tournament object
 * @param {Object} refund - Refund object
 * @param {string} errorMessage - Error message
 */
const sendRefundFailureNotifications = async (
  user,
  tournament,
  refund,
  errorMessage
) => {
  try {
    // Send email notification
    if (emailService.sendRefundFailedEmail) {
      await emailService.sendRefundFailedEmail(
        user,
        tournament,
        refund,
        errorMessage
      );
    }

    // Send SMS notification
    if (smsService.sendRefundFailedSMS && user.phoneNumber) {
      await smsService.sendRefundFailedSMS({
        mobile: user.phoneNumber,
        tournamentTitle: tournament.title,
        refundId: refund.refundTransactionId,
        errorMessage: errorMessage,
      });
    }
  } catch (error) {
    console.error("❌ Error sending refund failure notifications:", error);
    // Don't throw error to avoid affecting webhook response
  }
};

module.exports = {
  initiateRefund,
  getRefundStatus,
  handleRefundWebhook,
};
