JWT_SECRET=""
DB_USER='chessbrigade2'
DB_PASSWORD=1234
DB_NAME='chessbrigade2'
DB_PORT='5432'
DB_HOST='localhost'


BACKEND_URL="http://localhost:3000"

PAYU_CLIENT_ID=""
PAYU_CLIENT_SECRET=""
PAYU_KEY="gtKFFx"
PAYU_SALT="4R38IvwiV57FwVpsgOvTXBdLE4tHUXFW"
PAYU_PAYMENT_URL="https://test.payu.in/_payment"
FRONTEND_URL="http://localhost:5173"

# NODE_ENV="production"
NODE_ENV="development"



AWS_BUCKET_NAME=""
AWS_ACCESS_REGION=''
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""


# msg91 keys and template ids
MSG91_AUTH_KEY=""
MSG91_SENDER_ID=""
OTP_TEMPLATE_ID=""
TOURNAMENT_WITHDRAWAL_TEMPLATE_ID=""
PAYMENT_CONFIRMATION_TEMPLATE_ID=""
TOURNAMENT_REGISTRATION_TEMPLATE_ID=""
PAIRING_NOTIFICATION_TEMPLATE_ID=""


EMAIL_PORT=
EMAIL_SECURE=
EMAIL_USER=""
EMAIL_PASS=""
EMAIL_FROM="Chess Brigade <<EMAIL>>"
EMAIL = <EMAIL>



